pipeline {
  agent {
    node {
      label 'macOS-04'
    }
  }

  options {
    buildDiscarder(logRotator(daysToKeepStr: '15', numToKeepStr: '15'))
    ansiColor('xterm')
  }

  parameters {
    choice(name: 'customer', choices: ['78', '188'], description: '')
    choice(name: 'Mode', choices: ['APK', 'AAB'], description: 'Build Mode')
    choice(name: 'crypto_mode', choices: ['pqr', 'nonpqr'], description: '')
    choice(name: 'manage_mode', choices: ['1','0'], description: 'Manage Mode')
    choice(name: 'asset_environment', choices: ['release', 'staging', 'develop'], description: '')
    choice(name: 'assets_license_version', choices: ['3', '2'], description: '')
    choice(name: 'customer_environment', choices: ['UAT', 'POC', 'PROD'], description: '')
    string(name: 'vos_tools_version', defaultValue: '4.10.+', description: '')

    text(name: 'Profile', defaultValue: '', description: 'encrypted profile string (Optional)')
    string(name: 'vkeygen_version', defaultValue: 'latest.release', description: '')
    string(name: 'toolchain_version', defaultValue: 'latest.release', description: '')
    string(name: 'signature_version', defaultValue: 'latest.integration', description: '')
    string(name: 'signature_specific', defaultValue: 'vos-app-protection-signature', description: '')

    string(name: 'firmware_version', defaultValue: 'latest.integration', description: '')
    string(name: 'firmware_specifier', defaultValue: 'vos-firmware', description: 'Firmware specifier (e.g. dbs)')
    booleanParam(name: 'firmware_debug', defaultValue: false, description: 'Debuggable firmware (ONLY FOR NON PROD)')

    string(name: 'processor_android_version', defaultValue: 'latest.integration', description: '')
    string(name: 'vos_specifier_version', defaultValue: 'vos-processor-android', description: 'V-OS SDK specifier (e.g. dbs)')

    string(name: 'vguard_android_version', defaultValue: 'latest.integration', description: '')
    string(name: 'vguard_specifier_version', defaultValue: 'vos-app-protection-android', description: 'V-Guard SDK specifier (e.g. unobf)')

    string(name: 'smarttoken_android_version', defaultValue: 'latest.integration', description: '')
    string(name: 'smarttoken_specifier_version', defaultValue: 'vos-smarttoken-android', description: 'VTap SDK specifier (e.g. unobf)')

    string(name: 'otp_android_version', defaultValue: 'latest.integration', description: '')
    string(name: 'otp_specifier_version', defaultValue: 'otp-android', description: 'OTP SDK specifier (e.g. unobf)')
  }


  environment {
    DEVELOPER_DIR = '/Applications/Xcode.app/Contents/Developer'
    ANDROID_NDK_HOME = tool(type: 'com.cloudbees.jenkins.plugins.customtools.CustomTool', name: 'android-ndk-r12b')
    ANDROID_AVD_HOME = "${WORKSPACE}/.emulator"
    PATH = "$ANDROID_HOME/platform-tools:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/tools/bin:$ANDROID_NDK_HOME:$PATH"
  }

  tools {
    jdk '20'
    nodejs '18'
  }

  stages {
    stage('Install node modules') {
        steps{
            sh 'npm install'
        }
    }
    stage('Setup'){
      steps{
        dir('android') {
            sh 'chmod +x gradlew'
            sh "echo 'asset_environment=${params.asset_environment}' >> gradle.properties"
            sh "echo 'vkeygen_version=${params.vkeygen_version}' >> gradle.properties"
            sh "echo 'signature_version=${params.signature_version}' >> gradle.properties"

            sh "echo 'firmware_version=${params.firmware_version}' >> gradle.properties"

            sh "echo 'processor_android_version=${params.processor_android_version}' >> gradle.properties"

            sh "echo 'vguard_android_version=${params.vguard_android_version}' >> gradle.properties"

            sh "echo 'smarttoken_android_version=${params.smarttoken_android_version}' >> gradle.properties"

            sh "echo 'otp_android_version=${params.otp_android_version}' >> gradle.properties"
            sh "printenv"
            sh "mkdir -p ${WORKSPACE}/.emulator"
            sh "echo -------------- DEPENDENCIES -------------- "
            sh "./gradlew -version"
            sh "./gradlew :app:dependencies --refresh-dependencies | grep .vkey. | sort -u"
            sh "echo -------------- DEPENDENCIES -------------- "
        }
      }
    }
    stage('Parse Profile'){
      when {
        expression { return (params.Profile != '') }
      }
      steps {
        dir('android') {
          sh "mkdir -p app/src/main/assets/"
          writeFile file: "app/src/main/assets/profile", text: "${params.Profile}"
        }
      }
    }
    stage('trigger assets pipeline') {
      steps {
        script {
            generate_path = "non-production-assets-generation/non-prod-assets/"
            jobResults=build job: generate_path,
                    parameters: [
                        string(name: 'environment', value: "${params.asset_environment}"),
                        booleanParam(name: 'generate_asset', value: true),
                        string(name: 'crypto_mode', value: "${params.crypto_mode}"),
                        string(name: 'customer', value: "${params.customer}"),
                        string(name: 'customer_environment', value: "${params.customer_environment}"),
                        string(name: 'vos_tools_version', value: "${params.vos_tools_version}"),
                        string(name: 'vkeygen_version', value: "${params.vkeygen_version}"),
                        string(name: 'VOS_firmware_Src', value: "${params.firmware_specifier}"),
                        string(name: 'firmware_version', value: "${params.firmware_version}"),
                        booleanParam(name: 'firmware_debug', value: params.firmware_debug),
                        string(name: 'toolchain_version', value: "${params.toolchain_version}"),
                        string(name: 'VOS_signature_Src', value: "${params.signature_specific}"),
                        string(name: 'signature_version', value: "${params.signature_version}"),
                        string(name: 'VOS_nativeSign_Src', value: "${params.vos_specifier_version}"),
                        string(name: 'nativesign_version', value: "${params.processor_android_version}"),
                        string(name: 'vklicPackVer', value: "${params.assets_license_version}")
                    ],
                    propagate: true,
                    wait: true

            // List of values: https://stackoverflow.com/questions/46262862/how-to-i-get-the-url-of-build-triggered-with-build-step-on-jenkins
                if (jobResults.result == "SUCCESS") {
                    buildNumber = "${jobResults.number}"
                    echo "Build number |${buildNumber}| result: |${jobResults.result}|"
                    echo "See details on: |${jobResults.absoluteUrl}|"

                    copyArtifacts(projectName: generate_path, selector: specific("${buildNumber}"), target: 'android');
                }
        }
      }
    }
    stage('Copy assets') {
        steps {
            script {
                if (jobResults.result == "SUCCESS") {
                    dir('android') {
                        sh "./extract-assets.sh ${params.customer} ${params.manage_mode}"
                    }
                }
            }
        }
    }
    stage('Build APK'){
        when {
            expression { return (params.Mode == 'APK') }
        }
        steps {
            dir('android') {
                sh "./gradlew assembleDebug --refresh-dependencies -i"
                sh "./gradlew assembleRelease --refresh-dependencies -i"
            }
        }
    }
    stage('Build AAB'){
        when {
            expression { return (params.Mode == 'AAB') }
        }
        steps {
            dir('android') {
                sh "./gradlew bundleDebug --refresh-dependencies -i"
                sh "./gradlew bundleRelease --refresh-dependencies -i"
                sh "bundletool build-apks --bundle=app/build/outputs/bundle/debug/app-debug.aab --output=app-debug.apks --overwrite --ks=./app/vtap.jks --ks-pass=pass:vtapkey --ks-key-alias=VTapKey --key-pass=pass:vtapkey"
                sh "bundletool build-apks --bundle=app/build/outputs/bundle/release/app-release.aab --output=app-release.apks --overwrite --ks=./app/vtap.jks --ks-pass=pass:vtapkey --ks-key-alias=VTapKey --key-pass=pass:vtapkey"
            }
        }
    }

    stage('Archive'){
      steps {
        archiveArtifacts artifacts: 'android/app/**/*.aar, android/app/**/*.apk, android/app/**/*.aab, android/**/*.apks, Company_*.zip', fingerprint: true
      }
    }
  }
  post {
    always {
      publishHTML([allowMissing: true, alwaysLinkToLastBuild: false, keepAll: true, reportDir: 'app/build/reports', reportFiles: 'lint-results.html', reportName: 'Testapp Lint Results', reportTitles: ''])
      cleanWs cleanWhenFailure: false, cleanWhenUnstable: false
    }
  }
}
