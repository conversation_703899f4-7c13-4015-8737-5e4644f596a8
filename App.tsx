import React, { useRef, useEffect, useState } from 'react';
import type { PropsWithChildren } from 'react';
import {
  Alert,
  AppState,
  Button,
  DeviceEventEmitter,
  Platform,
  Pressable,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  useColorScheme,
  View,
} from 'react-native';

import { Colors } from 'react-native/Libraries/NewAppScreen';

// @ts-ignore
import { VTapPlugin } from 'react-native-vtap';

/* ------------------------- Start Screen ------------------------- */

function StartScreen({ onStart }: { onStart: () => void }) {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: 'white' }}>
      <View style={startStyles.container}>
        <Text style={startStyles.title}>Start profiling at this screen and click start to setup VTap</Text>

        <Pressable
          onPress={onStart}
          style={({ pressed }) => [startStyles.button, pressed && startStyles.buttonPressed]}
          accessibilityRole="button"
          accessibilityLabel="Start"
          testID="start-button"
        >
          <Text style={startStyles.buttonText}>Start</Text>
        </Pressable>
      </View>
    </SafeAreaView>
  );
}

/* ----------------------- Home Screen ----------------------- */

type SectionProps = PropsWithChildren<{ title: string }>;

function Section({ children }: SectionProps): JSX.Element {
  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.ButtonStyle}>{children}</Text>
    </View>
  );
}

function App(): JSX.Element {
  const [started, setStarted] = useState(false);

  const [logs, setLogs] = useState('');
  const [logItem, setLogItem] = useState('');
  const [tokenSerial, onChangeTokenSerial] = useState('Token Serial');
  const [Apin, onChangeApin] = useState('Apin');

  const appState = useRef(AppState.currentState);
  const [, setAppStateVisible] = useState(appState.current);

  const getData = async () => {
    try {
      const url = `https://api.restful-api.dev/objects`;
      const response = await fetch(url);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
      const result = await response.json();
      await new Promise(resolve => setTimeout(resolve, 30000));
      return { result };
    } catch (error) {
      console.error('Error fetching data:', error);
      return null;
    }
  };

  const printLogs = (log: string) => {
    setLogItem(log);
    console.log(log);
  };

  useEffect(() => {
    if (!logItem) return;
    setLogs(prev => (prev ? prev + '\n\n' + logItem : logItem));
  }, [logItem]);

  useEffect(() => {
    DeviceEventEmitter.addListener(VTapPlugin.VGUARD_EVENTS, onVGuardEvents);
    DeviceEventEmitter.addListener(VTapPlugin.VTAP_EVENTS, onVTapEvents);
    return () => {
      // Remove the event listener before the component is destroyed.
      DeviceEventEmitter.removeAllListeners(VTapPlugin.VGUARD_EVENTS);
      DeviceEventEmitter.removeAllListeners(VTapPlugin.VTAP_EVENTS);
    };
  }, []);

  useEffect(() => {
    if (!started) return;

    const subscription = AppState.addEventListener('change', nextAppState => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        console.log('App has come to the foreground!');
      }
      appState.current = nextAppState;
      setAppStateVisible(appState.current);
      console.log('AppState', appState.current);
    });

    return () => subscription.remove();
  }, [started]);

  const setupVTap = () => {
    printLogs('START VTAP');
    // Set url of synchronizing the vos logs if enabled
    VTapPlugin.setThreatIntelligenceServerURL('https://stg-cloud.v-key.com/')
    VTapPlugin.setHostName('https://stg-cloud.v-key.com/provision', 'https://stg-cloud.v-key.com/vtap');
    VTapPlugin.setupVTap();
  };

  const onVTapEvents = async (event: any) => {
    const action = event.action;
    printLogs('. vtap.event.action: ' + event.action);
    if(action == VTapPlugin.VTAP_SETUP_ACTION) {
        const vtapSetupStatus = event.data;
        printLogs('Vtap setup status: '+ vtapSetupStatus);
        if(vtapSetupStatus == 40208) {
          printLogs('Emulator detected!');
        }
        if(vtapSetupStatus == 40204) {
          // start new provisioning token
        }
    }
  };

const onVGuardEvents = async (event: any) => {
    const action = event.action;
    printLogs('. event.action: ' + event.action);

    if (action == VTapPlugin.VOS_READY) {
      printLogs('v-os return code: ' + event.data);

      const errorCode = event.data;

      const tid = await VTapPlugin.getTroubleshootingId();
      printLogs('^TID: ' + tid);

    } else if (action == VTapPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) { // This event is for Android only
      showWarningAlert('app is running on Virtual Space')
    } else if (action == VTapPlugin.VGUARD_OVERLAY_DETECTED) { // This event is for Android only
      showWarningAlert('OVERLAY DETECTED!')
    } else if (action == VTapPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
      if (Platform.OS === 'android') {
        var _data = 'VGUARD_SCREEN_SHARING_DETECTED'
        const data = event.data;
        if (data != null && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            _data += '\nScreen Name: ' + data[i];
          }
        }
        showWarningAlert('OVERLAY DETECTED!')
      } else if (Platform.OS === 'ios') {
        showWarningAlert('SCREEN_SHARING DETECTED!')
      }

    } else if (action == VTapPlugin.VGUARD_HANDLE_THREAT_POLICY) {
      const data = event.data;
      // printLogs("data: " + data);
      // printLogs('highest_policy: ' + data.highest_policy);
      // printLogs('alertTitle: ' + data.alertTitle);
      // printLogs('alertMessage: ' + data.alertMessage);
      // printLogs('disabledAppExpired: ' + data.disabledAppExpired);
      
      const threats = data.threats;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //   threatInfo.ThreatClass +
          //   ' - ' +
          //   threatInfo.ThreatName +
          //   ' - ' +
          //   threatInfo.ThreatInfo +
          //   ' - ' +
          //   threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('VGUARD_HANDLE_THREAT_POLICY, no threats found!');
      }
    } else  {
      printLogs('. event.data: ' + event.data);
    }
  };

  const showWarningAlert = (message: string) => {
    printLogs(message);
    Alert.alert('Warning', message, [{ text: 'OK', onPress: () => '' }]);
  };

  const isDarkMode = useColorScheme() === 'dark';

  if (!started) {
    return <StartScreen onStart={() => {
      setStarted(true)
      setupVTap()
    }} />;
  }

  const provisionOTP = async (ts: string, pin: string) => {
    console.log('Trigger VTapPlugin.provisionOTP');
  
    const tsTrim = ts.trim();
    const pinTrim = pin.trim();
  
    if (!tsTrim || !pinTrim) {
      console.warn('TS or PIN is empty');
      return;
    }
  
    try {
      const resultCode = await VTapPlugin.getLoadAckTokenFirmware([tsTrim, pinTrim]);
  
      if (resultCode === 40600 /* VTAP_TOKEN_DOWNLOAD_SUCCESS */ ||
          resultCode === 40608 /* VTAP_LOAD_FIRMWARE_SUCCESS */) {
        console.log('Provision success 🎉');
        printLogs?.('Provision success 🎉');
      } else {
        console.log('Provision failed ❌, code:', resultCode);
        printLogs?.('Provision failed ❌, code: ' + resultCode);
      }
    } catch (error) {
      console.error('Provision OTP failed:', error);
    }
  };

  const generateOTP = async (ts: string, tokenPin: string) => {  
    try {
      const createPin = await VTapPlugin.createTokenPIN(tokenPin, ts);
      printLogs('createTokenPIN status: ' + createPin);
      const checkpin = await VTapPlugin.checkTokenPIN(tokenPin, true, ts, 0);
      printLogs('checkTokenPIN status: ' + checkpin);
      const resultCode = await VTapPlugin.setOtpLength(8);
      if (resultCode === 41000 /* VTAP_SET_OTP_LENGTH_SUCCESS */) {
        printLogs('setOtpLength success 🎉');
        const otp = await VTapPlugin.generateTOTP(1);
        printLogs('otp: ' + otp);
      } else {
        printLogs('setOtpLength failed ❌, code: ' + resultCode);
      }
    } catch (error) {
      console.error('Provision OTP failed:', error);
    }
  };

  const SafeAreaViewStyle = { backgroundColor: Colors.white };
  const scrollViewStyle = { height: 300, backgroundColor: 'lightgray', borderRadius: 5, margin: 10 };
  const logStyle = { color: 'black' };

  return (
    <SafeAreaView style={SafeAreaViewStyle}>
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <View style={styles.sectionContainer}>
        <TextInput
          style={styles.input}
          placeholder="Token Serial"
          onChangeText={onChangeTokenSerial}
          value={tokenSerial}
        />
      </View>

      <View style={styles.sectionContainer}>
        <TextInput
          style={styles.input}
          placeholder="Apin"
          onChangeText={onChangeApin}
          value={Apin}
        />
      </View>

      <Section title="">
        <Button
          onPress={() => {
            printLogs('Provisioning');
            provisionOTP(tokenSerial, Apin)
          }}
          title="Provisioning"
          color="#841584"
        />
        <View style={{ padding: 2 }} />

        <Button
          onPress={() => {
            printLogs('generate OTP');
            generateOTP(tokenSerial, "123456")
          }}
          title="generate OTP"
          color="#841584"
        />
        <View style={{ padding: 2 }} />
      </Section>

      <Section title="Logs" />
      <ScrollView style={scrollViewStyle}>
        <View style={styles.sectionContainer}>
          <Text style={logStyle}> {logs} </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

/* ----------------------------- Styles ---------------------------- */

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 10,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  ButtonStyle: {
    backgroundColor: Colors.lighter,
  },
  highlight: {
    fontWeight: '700',
  },
  input: {
    height: 40,
    width: 330,
    margin: 12,
    borderWidth: 1,
    padding: 10,
    backgroundColor: Colors.lighter,
    color: Colors.black,
  },
});

const startStyles = StyleSheet.create({
  container: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 24 },
  title: { fontSize: 22, fontWeight: '700', marginBottom: 16, color: '#111827' },
  button: {
    minWidth: 160,
    paddingVertical: 14,
    paddingHorizontal: 20,
    backgroundColor: '#4f46e5',
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonPressed: { opacity: 0.9, transform: [{ scale: 0.98 }] },
  buttonText: { color: 'white', fontSize: 16, fontWeight: '700', letterSpacing: 0.3 },
});

export default App;