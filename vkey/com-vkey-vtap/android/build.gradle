apply plugin: 'com.android.library'
apply plugin:'base'
android {
    compileSdkVersion 34
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 4
        versionName "4.10.4"
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    buildFeatures {
        viewBinding true
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.facebook.react:react-native:+'

    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // VKey SDK
    implementation(files("libs/cuckoofilter4j-1.0.2-customized-jar-without-dependencies.jar"))

    releaseImplementation(group: 'com.vkey', name: proj_processor_android_specifier, version: proj_processor_android_version, ext: 'aar', changing: true)
    debugImplementation(group: 'com.vkey', name: proj_processor_android_specifier, version: proj_processor_android_version, classifier: 'Debug', ext: 'aar', changing: true)

    debugImplementation(group: 'com.vkey.vguard', name: proj_vguard_android_specifier, version: proj_vguard_android_version, classifier: 'Debug', ext: 'aar', changing: true)
    releaseImplementation(group: 'com.vkey.vguard', name: proj_vguard_android_specifier, version: proj_vguard_android_version, ext: 'aar', changing: true)

    releaseImplementation(group: 'com.vkey.vtap', name: proj_smarttoken_android_specifier, version: proj_smarttoken_android_version, ext: 'aar', changing: true)
    debugImplementation(group: 'com.vkey.vtap', name: proj_smarttoken_android_specifier, version: proj_smarttoken_android_version, classifier: 'Debug', ext: 'aar', changing: true)

    debugImplementation(group: 'com.vkey.otp', name: proj_otp_android_specifier, version: proj_otp_android_version, classifier: 'Debug', ext: 'aar', changing: true)
    releaseImplementation(group: 'com.vkey.otp', name: proj_otp_android_specifier, version: proj_otp_android_version, ext: 'aar', changing: true)
    // VKey SDK dependecies
    implementation 'com.getkeepsafe.relinker:relinker:1.4.4'
    implementation 'com.google.guava:guava:32.1.2-android'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.7.0'

    implementation 'com.google.code.gson:gson:2.8.6'
    api 'io.jsonwebtoken:jjwt-api:0.10.7'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.10.7'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.10.7') {
        exclude group: 'org.json', module: 'json' //provided by Android natively
    }
}
  