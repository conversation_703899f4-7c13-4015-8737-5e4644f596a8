<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.vkey.vtap">

    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.DETECT_SCREEN_CAPTURE" />
    <application
        android:label="@string/app_name"
        android:theme="@style/AppTheme">

<!--        <provider-->
<!--            android:name="com.vkey.android.vguard.VGStartUpDetector"-->
<!--            android:authorities="{applicationId}.start-up.detector"-->
<!--            android:exported="false" />-->
    </application>
    <!--     Required when targeting Android 11 (API level 30) or higher-->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>
</manifest>
  