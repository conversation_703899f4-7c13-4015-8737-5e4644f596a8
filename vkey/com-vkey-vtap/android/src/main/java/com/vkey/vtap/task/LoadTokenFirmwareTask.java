package com.vkey.vtap.task;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import com.facebook.react.bridge.Promise;
import com.vkey.android.vtap.VTapInterface;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class LoadTokenFirmwareTask {
    public Promise promise;
    public VTapInterface iVTapManager;
    ArrayList<String> provisioningInfo;
    Context context;

    public LoadTokenFirmwareTask(Context context, ArrayList<String> provisioningInfo) {
        this.context = context;
        this.provisioningInfo = provisioningInfo;
    }

    public void execute() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());

        executor.execute(() -> {
            String tokenFileName = provisioningInfo.get(0);
            //copy files from asset to internal storage
            String tokenFilePath = context.getFilesDir().getPath() + File.separator + tokenFileName;
            try {
                OutputStream myOutput = new FileOutputStream(tokenFilePath);
                byte[] buffer = new byte[1024];
                int length;
                InputStream myInput = context.getAssets().open(tokenFileName);
                while ((length = myInput.read(buffer)) > 0) {
                    myOutput.write(buffer, 0, length);
                }
                myInput.close();
                myOutput.flush();
                myOutput.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }

            // Load the downloaded Token Firmware
            int result = iVTapManager.loadTokenFirmware(provisioningInfo.get(0), provisioningInfo.get(1), tokenFilePath);

            handler.post(() -> {
                promise.resolve(result);
            });
        });
    }
}
