package com.vkey.vtap;

import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_FINISH;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_SCAN_COMPLETE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.PROFILE_LOADED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_AIRDROID_PORT_IS_OPEN;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_TITLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DEVELOPER_OPTIONS_ENABLED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DISABLED_APP_EXPIRED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_HANDLE_THREAT_POLICY;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_HIGHEST_THREAT_POLICY;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_NETWORK_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED_DISABLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SCREEN_SHARING_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SSL_ERROR_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_STATUS;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_SPACE_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VOS_READY;
import static com.vkey.android.vguard.model.VGThreatPolicy.BY_PASS;
import static com.vkey.android.vtap.VTapInterface.SEED_CR;
import static com.vkey.android.vtap.VTapInterface.SEED_TOTP1;
import static com.vkey.android.vtap.VTapInterface.SEED_TOTP2;
import static com.vkey.android.vtap.VTapInterface.SEED_TXS;
import static com.vkey.android.vtap.VTapInterface.TokenType.UNKNOWN_TOKEN;
import static com.vkey.android.vtap.utility.ResultCode.VTAP_SETUP_SUCCESS;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.LifecycleEventListener;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.vkey.android.internal.vguard.engine.BasicThreatInfo;
import com.vkey.android.vguard.FeatureToggleManager;
import com.vkey.android.vguard.LocalBroadcastManager;
import com.vkey.android.vguard.MemoryConfiguration;
import com.vkey.android.vguard.VGuardBroadcastReceiver;
import com.vkey.android.vguard.VGuardFactory;
import com.vkey.android.vguard.VGuardLifecycleHook;
import com.vkey.android.vguard.model.VGScanLevel;
import com.vkey.android.vguard.model.VGThreatPolicy;
import com.vkey.android.vguard.model.VGThreatResponse;
import com.vkey.android.vguard.model.VGVirtualTapType;
import com.vkey.android.vguard.model.VGuardNetworkType;
import com.vkey.android.vtap.VTapFactory;
import com.vkey.android.vtap.VTapInterface;
import com.vkey.vtap.common.Utility;
import com.vkey.vtap.task.LoadTokenFirmwareTask;
import com.vkey.vtap.task.ProvisioningTask;

import org.json.JSONArray;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import vkey.android.vos.VosWrapper;

public class VTapPlugin extends ReactContextBaseJavaModule implements LifecycleEventListener {
    private static final int ERROR = -1;
    private static final String ERR_UNEXPECTED_EXCEPTION = "ERR_UNEXPECTED_EXCEPTION";
    private static final String VTAP_SETUP_ACTION = "vkey.android.vtap.VTAP_SETUP";
    private static final String VTAP_SETUP_STATUS = "vkey.android.vtap.VTAP_SETUP_STATUS";
    private static final String VTAP_EVENTS = "vkey.android.vtap.events";
    private static final String VGUARD_EVENTS = "vkey.android.vguard.events";
    private static final String FIRMWARE_RETURN_CODE = "vkey.android.vguard.FIRMWARE_RETURN_CODE";
    private static final String RESET_VOS_STORAGE = "vkey.android.vguard.resetVOSTrustedStorageRvcr";
    private final ReactApplicationContext reactContext;
    private static VTapInterface iVTapManager;
    // LifecycleHook to notify VGuard of activity's lifecycle
    public static VGuardLifecycleHook hook;
    private String tiUrl = null;
    private String dialogTitle = "";
    private String dialogMessage = "";
    private static final ArrayList<VGThreatResponse> dialogThreats = new ArrayList<>();
    private VGThreatPolicy highestThreatPolicy = null;
    private int vtapSetupStatus;
    private MemoryConfiguration mMemoryConfiguration = MemoryConfiguration.DEFAULT;
    private boolean isAllowsArbitraryNetworking = false;
    private boolean isDebug = false;
    private boolean isVirtualTapDetectionEnabled = false;
    private boolean isUsePackageManagerEnabled = true;
    private boolean isOverlayDetectionEnabled = false;

    private static final String QA_TAG = "QA_Test_Tag";
    private static int scan_time = 0;
    private static long startTime = 0;

    public VTapPlugin(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
        reactContext.addLifecycleEventListener(this);
    }

    @NonNull
    @Override
    public String getName() {
        return "VTapPlugin";
    }

    @Override
    public Map<String, Object> getConstants() {
        // Export any constants to be used in your native module
        // https://facebook.github.io/react-native/docs/native-modules-android.html#the-toast-module
        final Map<String, Object> constants = new HashMap<>();
        constants.put("TokenType_OTP", VTapInterface.TokenType.OTP.getValue());
        constants.put("VTAP_SETUP_ACTION", VTAP_SETUP_ACTION);
        constants.put("VTAP_EVENTS", VTAP_EVENTS);
        // VGuard receiver
        constants.put("VGUARD_EVENTS", VGUARD_EVENTS);
        constants.put("VOS_READY", VOS_READY);
        constants.put("VGUARD_STATUS", VGUARD_STATUS);
        constants.put("VGUARD_OVERLAY_DETECTED", VGUARD_OVERLAY_DETECTED);
        constants.put("VGUARD_OVERLAY_DETECTED_DISABLE", VGUARD_OVERLAY_DETECTED_DISABLE);
        constants.put("VGUARD_PROFILE_LOADED", PROFILE_LOADED);

        // scan threats
        constants.put("ACTION_FINISH", ACTION_FINISH);
        constants.put("ACTION_SCAN_COMPLETE", ACTION_SCAN_COMPLETE);

        // 4.9
        constants.put("RESET_VOS_STORAGE", RESET_VOS_STORAGE);
        constants.put("VGUARD_VIRTUAL_SPACE_DETECTED", VGUARD_VIRTUAL_SPACE_DETECTED);
        constants.put("VGUARD_SCREEN_SHARING_DETECTED", VGUARD_SCREEN_SHARING_DETECTED);
        constants.put("VGUARD_SSL_ERROR_DETECTED", VGUARD_SSL_ERROR_DETECTED);
        constants.put("VGUARD_HANDLE_THREAT_POLICY", VGUARD_HANDLE_THREAT_POLICY);
        constants.put("VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED", VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED);
        //4.10
        constants.put("VGUARD_DEVELOPER_OPTIONS_ENABLED", VGUARD_DEVELOPER_OPTIONS_ENABLED);
        constants.put("VGUARD_AIRDROID_PORT_IS_OPEN", VGUARD_AIRDROID_PORT_IS_OPEN);
        constants.put("VGUARD_VIRTUAL_TAP_DETECTED", VGUARD_VIRTUAL_TAP_DETECTED);
        constants.put("VGUARD_NETWORK_DETECTED", VGUARD_NETWORK_DETECTED);

        constants.put("SEED_TOTP1", SEED_TOTP1);
        constants.put("SEED_TOTP2", SEED_TOTP2);
        constants.put("SEED_CR", SEED_CR);
        constants.put("SEED_TXS", SEED_TXS);
        return constants;
    }

    @Override
    public void onHostResume() {
        if (iVTapManager != null) {
            iVTapManager.onResume(reactContext);
        }
    }

    @Override
    public void onHostPause() {
        if (iVTapManager != null) {
            iVTapManager.onPause();
        }
    }

    @Override
    public void onHostDestroy() {
        if (mVtapRcvr != null) {
            LocalBroadcastManager.getInstance(reactContext).unregisterReceiver(mVtapRcvr);
            mVtapRcvr = null;
        }
        if (mVguardRcvr != null) {
            LocalBroadcastManager.getInstance(reactContext).unregisterReceiver(mVguardRcvr);
            mVguardRcvr = null;
        }
        // To be called only if app is exiting. Not to be called on every activity exit.
        // This call will destroy vguard, vtap and VOS.
        if (iVTapManager != null) {
            iVTapManager.onDestroy();
        }
    }

    private VTapInterface getVTapManager() {
        if (iVTapManager == null) {
            iVTapManager = VTapFactory.getInstance(reactContext);
        }
        return iVTapManager;
    }

    @ReactMethod
    public void setHostName(String provServer, String vtapServer) {
        getVTapManager().setHostName(provServer, vtapServer);
    }
    private void setupReceivers() {
        // setup broadcast receiver
        LocalBroadcastManager localBroadcastManager = LocalBroadcastManager.getInstance(reactContext);
        if (mVtapRcvr == null) {
            mVtapRcvr = initializeVTapReceiver();
            localBroadcastManager.registerReceiver(mVtapRcvr, new IntentFilter(VTAP_SETUP_ACTION));
        }

        if (mVguardRcvr == null) {
            mVguardRcvr = initializeVGuardReceiver();
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(ACTION_FINISH));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(ACTION_SCAN_COMPLETE));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VOS_READY));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(PROFILE_LOADED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED_DISABLE));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_STATUS));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_VIRTUAL_SPACE_DETECTED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_SCREEN_SHARING_DETECTED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_DEVELOPER_OPTIONS_ENABLED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_AIRDROID_PORT_IS_OPEN));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_VIRTUAL_TAP_DETECTED));
            localBroadcastManager.registerReceiver(mVguardRcvr, new IntentFilter(VGUARD_NETWORK_DETECTED));
        }
    }

    @ReactMethod
    public void setupVTap() {
        Log.d(QA_TAG, "Start setup VTap");
        startTime = System.currentTimeMillis();
        setupReceivers();
        setConfiguration();
        getVTapManager().setupVTap();
    }

    @ReactMethod
    public void setLoggerBaseUrl(String url) {
        VosWrapper.getInstance(reactContext).setLoggerBaseUrl(url);
    }

    /** Remember to call other reactMethods to set value before calling this method.*/
    public void setConfiguration() {
        getVTapManager().setConfiguration(new VGuardFactory.Builder()
                .setMemoryConfiguration(mMemoryConfiguration)
                .setAllowsArbitraryNetworking(isAllowsArbitraryNetworking)
                .setDebugable(isDebug)
                .setVirtualTapDetectionEnabled(isVirtualTapDetectionEnabled)
                .setUsePackageManager(isUsePackageManagerEnabled));
        //Overlay Detection
        FeatureToggleManager featureToggleManager = FeatureToggleManager.getInstance();
        featureToggleManager.enableGenericFeature(FeatureToggleManager.FeatureName.OVERLAY_DETECTION, isOverlayDetectionEnabled);
    }

    @ReactMethod
    public void getDFPHash(Promise promise) {
        String dfHash = null;
        if (iVTapManager != null) {
            dfHash = iVTapManager.getDFPHash();
        }
        if(promise != null) {
            promise.resolve(dfHash);
        }
    }

    @ReactMethod
    public void isProvisioningDone(Promise promise) {
        boolean isProvisioningDone = false;
        if (iVTapManager != null) {
            isProvisioningDone = iVTapManager.isProvisioningDone();
        }
        if(promise != null) {
            promise.resolve(isProvisioningDone);
        }
    }

    @ReactMethod
    public void getAllProvisionedTokens(Promise promise) {
        WritableArray resultArray = Arguments.createArray();
        if(iVTapManager != null) {
            String[] provisionedTokens = iVTapManager.getAllProvisionedTokens();
            resultArray = Utility.toWritableArray(provisionedTokens);
        }

        if(promise != null) {
            promise.resolve(resultArray);
        }
    }


    // OTP, PKI, UNKNOWN_TOKEN;
    @ReactMethod
    public void getTokenType(String tokenSerial, Promise promise) {
        VTapInterface.TokenType tokenType = UNKNOWN_TOKEN;
        if (iVTapManager != null) {
            tokenType = iVTapManager.getTokenType(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(tokenType.getValue());
        }
    }

    // Generic : Troubleshooting Logs API
    @ReactMethod
    public void getTroubleshootingId(Promise promise) {
        String troubleshootingId = null;
        if (iVTapManager != null) {
            troubleshootingId = iVTapManager.getTroubleshootingId();
        }
        if(promise != null) {
            promise.resolve(troubleshootingId);
        }
    }


    @ReactMethod()
    public void sendTroubleshootingLogs(Promise promise) {
        try {
            int result = iVTapManager.sendTroubleshootingLogs();
            promise.resolve(result);
        } catch (Exception ex) {
            promise.reject(ERR_UNEXPECTED_EXCEPTION, ex);
        }
    }

    @ReactMethod()
    public void getTrustedTime(Promise promise) {
        int getTrustedTime = ERROR;
        if (iVTapManager != null) {
            getTrustedTime = iVTapManager.getTrustedTime();
        }
        if(promise != null) {
            promise.resolve(getTrustedTime);
        }
    }

    /*========================= OTP API Starts =========================*/
    //  OTP TA: Transaction API

    @ReactMethod
    public void setOtpLength(int length, Promise promise) {
        int result = ERROR;
        if (iVTapManager != null) {
            result = iVTapManager.setOtpLength(length);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }


    @ReactMethod
    public void generateTOTP(int num, Promise promise) {
        try {
            ArrayList<String> generateTOTP = iVTapManager.generateTOTP(num);
            WritableArray values = Utility.convertToWritableArray(generateTOTP);
            promise.resolve(values);
        } catch (Exception ex) {
            promise.reject(ERR_UNEXPECTED_EXCEPTION, ex);
        }
    }

    @ReactMethod
    public void generateCR(String msg, Promise promise) {
        try {
            ArrayList<String> generateCR = iVTapManager.generateCR(msg);
            WritableArray values = Utility.convertToWritableArray(generateCR);
            promise.resolve(values);
        } catch (Exception ex) {
            promise.reject(ERR_UNEXPECTED_EXCEPTION, ex);
        }
    }

    @ReactMethod
    public void generateTxS(String account, String amount, Promise promise) {
        try {
            ArrayList<String> generateTxs = iVTapManager.generateTxS(account, amount);
            WritableArray values = Utility.convertToWritableArray(generateTxs);
            promise.resolve(values);
        } catch (Exception ex) {
            promise.reject(ERR_UNEXPECTED_EXCEPTION, ex);
        }
    }

    @ReactMethod
    public void getTokenSerial(Promise promise) {
        String ts = null;
        if(iVTapManager != null) {
            ts = iVTapManager.getTokenSerial();
        }
        if(promise != null) {
            promise.resolve(ts);
        }
    }

    /*========================= Multiple TA API Starts =========================*/

    // Multiple TA (OTP / PKI): Provisioning API
    @ReactMethod
    public void validateCheckSum(ReadableArray provisioningInfo, Promise promise) {
        boolean result = false;
        if(iVTapManager != null) {
            ArrayList<String> arrProvisioningInfo = Utility.toArrayListString(provisioningInfo);
            result = iVTapManager.validateCheckSum(arrProvisioningInfo);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void getLoadAckTokenFirmware(ReadableArray provisioningInfo, Promise promise) {
        ArrayList<String> arrProvisioningInfo = Utility.toArrayListString(provisioningInfo);
        //decrypt Apin before provisioning
        String decryptedApin = Utility.decryptApin(arrProvisioningInfo.get(1));
        arrProvisioningInfo.remove(1);
        arrProvisioningInfo.add(1, decryptedApin);
        ProvisioningTask provisioningTask = new ProvisioningTask(arrProvisioningInfo, false);
        provisioningTask.promise = promise;
        provisioningTask.iVTapManager = iVTapManager;
        provisioningTask.execute();
    }

    // New Api support http Post/delete Ack
    @ReactMethod
    public void getLoadAckTokenFirmwareWithHttpPost(ReadableArray provisioningInfo, boolean isHttpPost, Promise promise) {
        ArrayList<String> arrProvisioningInfo = Utility.toArrayListString(provisioningInfo);
        //decrypt Apin before provisioning
        String decryptedApin = Utility.decryptApin(arrProvisioningInfo.get(1));
        arrProvisioningInfo.remove(1);
        arrProvisioningInfo.add(1, decryptedApin);
        ProvisioningTask provisioningTask = new ProvisioningTask(arrProvisioningInfo, isHttpPost);
        provisioningTask.promise = promise;
        provisioningTask.iVTapManager = iVTapManager;
        provisioningTask.execute();
    }

    @ReactMethod
    public void loadTokenFirmware(String tokenSerial, String aPin, String downloadFilePath, Promise promise) {
        ArrayList<String> provisioningInfo = new ArrayList<>();
        provisioningInfo.add(tokenSerial);
        provisioningInfo.add(aPin);
        LoadTokenFirmwareTask loadTokenFirmwareTask = new LoadTokenFirmwareTask(reactContext, provisioningInfo);
        loadTokenFirmwareTask.promise = promise;
        loadTokenFirmwareTask.iVTapManager = iVTapManager;
        loadTokenFirmwareTask.execute();
    }

    @ReactMethod
    public void removeTokenFirmware(String tokenSerial, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
          result =  iVTapManager.removeTokenFirmware(tokenSerial);
        }
        if(promise != null) {
          promise.resolve(result);
        }
    }

    @ReactMethod
    public void loadToken(String tokenSerial, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
            result =  iVTapManager.loadToken(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void unloadToken(String tokenSerial, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
            result =  iVTapManager.unloadToken(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void getTokenFirmwareVersion(String tokenSerial, Promise promise) {
        String version = null;
        if(iVTapManager != null) {
            version = iVTapManager.getTokenFirmwareVersion(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(version);
        }
    }

    @ReactMethod
    public void isTokenRegistered(String tokenSerial, Promise promise) {
        boolean isTokenRegistered = false;
        if(iVTapManager != null) {
            isTokenRegistered = iVTapManager.isTokenRegistered(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(isTokenRegistered);
        }
    }

    @ReactMethod
    public void createTokenPIN(String pin, String tokenSerial, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
            result = iVTapManager.createTokenPIN(pin, tokenSerial);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void checkTokenPIN(String pin, boolean rememberPin, String tokenSerial, int seedNumber, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
            if(seedNumber == 0) {
                result = iVTapManager.checkTokenPIN(pin, rememberPin, tokenSerial);
            }
            else {
                result = iVTapManager.checkTokenPIN(pin, rememberPin, tokenSerial, seedNumber);
            }
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void isTokenPINRemembered(String tokenSerial, Promise promise) {
        boolean isTokenPINRemembered = false;
        if(iVTapManager != null) {
            isTokenPINRemembered = iVTapManager.isTokenPINRemembered(tokenSerial);
        }
        if(promise != null) {
            promise.resolve(isTokenPINRemembered);
        }
    }

    @ReactMethod
    public void changeTokenPIN(String oldPIN, String newPIN, String tokenSerial, Promise promise) {
        int result = ERROR;
        if(iVTapManager != null) {
            result = iVTapManager.changeTokenPIN(oldPIN, newPIN , tokenSerial);
        }
        if(promise != null) {
            promise.resolve(result);
        }
    }

    @ReactMethod
    public void setMemoryConfiguration(int config) {
        mMemoryConfiguration = config == 0 ? MemoryConfiguration.DEFAULT : MemoryConfiguration.HIGH;
    }
    public void setDebuggable(boolean debuggable) {
        this.isDebug = debuggable;
        VGuardFactory.debug = debuggable;
    }
    @ReactMethod
    public void setAllowsArbitraryNetworking(boolean enable) {
        isAllowsArbitraryNetworking = enable;
    }

    /** Remember to add provider in AndroidManifest.xml to use this feature.*/
    @ReactMethod
    public void setVirtualTapDetectionEnabled(boolean virtualTapDetectionEnabled) {
        isVirtualTapDetectionEnabled = virtualTapDetectionEnabled;
    }
    @ReactMethod
    public void setUsePackageManager(boolean usePackageManager) {
        isUsePackageManagerEnabled = usePackageManager;
    }
    @ReactMethod
    public void setOverlayDetectionEnabled(boolean isEnable) {
        this.isOverlayDetectionEnabled = isEnable;
    }
    @ReactMethod
    public void setThreatIntelligenceServerURL(String threadIntelUrl) {
        tiUrl = threadIntelUrl;
        getVTapManager().setThreatIntelligenceServerURL(threadIntelUrl);
    }

    /**
     * PRIVATE METHODS
     ****/


    private VGuardBroadcastReceiver mVguardRcvr;
    private VGuardBroadcastReceiver initializeVGuardReceiver() {
        Activity currentActivity = getCurrentActivity();
        return new VGuardBroadcastReceiver(currentActivity) {
            @Override
            public void onReceive(Context context, Intent intent) {
                super.onReceive(context, intent);
                String action = intent.getAction();
                if(action != null) {
                    switch (action) {
                        case ACTION_FINISH: {
                            Utility.sendEventEmitter(reactContext, ACTION_FINISH, null, VGUARD_EVENTS);
                            quitApp();
                            break;
                        }
                        case PROFILE_LOADED: {
                            Utility.sendEventEmitter(reactContext, PROFILE_LOADED, null, VGUARD_EVENTS);
                            break;
                        }
                        case VOS_READY: {
                            long vosFirmwareCode = intent.getLongExtra(FIRMWARE_RETURN_CODE, 0);
                            Log.d(QA_TAG, "vos return code:" + vosFirmwareCode);
                            iVTapManager = VTapFactory.getInstance(context);
                            if(vosFirmwareCode == -3 || vosFirmwareCode == -5){
                                iVTapManager.getAllProvisionedTokens();
                                iVTapManager.resetVOSTrustedStorage();
                                iVTapManager.onDestroy();
                                setupVTap();
                            } else {
                                Utility.sendEventEmitter(reactContext, VOS_READY, vosFirmwareCode, VGUARD_EVENTS);
                            }
                            break;
                        }
                        case ACTION_SCAN_COMPLETE: {
                            Log.d(QA_TAG, "Action scan complete");
                            scan_time++;
                            if(scan_time == 3) {
                                Log.d(QA_TAG, "Total time scan: " + toSecondsString(System.currentTimeMillis() - startTime));
                                scan_time = 0;
                                startTime = 0;
                            }
                            WritableArray arrayData = getArrayThreats(intent);
                            Utility.sendEventEmitter(reactContext, ACTION_SCAN_COMPLETE, arrayData, VGUARD_EVENTS);
                            if (intent.hasExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL)) {
                                VGScanLevel scanLevel = (VGScanLevel)intent.getSerializableExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL);
                                Utility.sendEventEmitter(reactContext, VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL, VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL + ": " + scanLevel, VGUARD_EVENTS);
                            }
                            break;
                        }
                        case VGUARD_OVERLAY_DETECTED: {
                            Utility.sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED, null, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_OVERLAY_DETECTED_DISABLE: {
                            Utility.sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED_DISABLE, null, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_VIRTUAL_SPACE_DETECTED: {
                            Utility.sendEventEmitter(reactContext, VGUARD_VIRTUAL_SPACE_DETECTED, null, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_STATUS: {
                            if (intent.hasExtra(VGUARD_HANDLE_THREAT_POLICY)) {
                                handleThreatPolicy(intent, VGUARD_HANDLE_THREAT_POLICY);
                            }
                            else if (intent.hasExtra(VGUARD_SSL_ERROR_DETECTED)) {
                                handleSslErrorDetection(intent, VGUARD_SSL_ERROR_DETECTED);
                            } else {
                                String message = intent.getStringExtra(VGUARD_MESSAGE);
                                Utility.sendEventEmitter(reactContext, VGUARD_STATUS, message, VGUARD_EVENTS);
                            }
                            break;
                        }
                        case VGUARD_SCREEN_SHARING_DETECTED: {
                            StringBuilder builder = new StringBuilder();
                            try {
                                String sharingDisplays = intent.getStringExtra(VGUARD_SCREEN_SHARING_DISPLAY_NAMES);
                                JSONArray jsonArray = new JSONArray(sharingDisplays);
                                builder.append(jsonArray);
                            } catch (Exception e) {
                                e.fillInStackTrace();
                            }
                            Utility.sendEventEmitter(reactContext, VGUARD_SCREEN_SHARING_DETECTED, builder.toString(), VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED: {
                            StringBuilder builder = new StringBuilder();
                            try {
                                String sideloadlist = intent.getStringExtra(VGUARD_SIDELOADED_RESULT);
                                if (!TextUtils.isEmpty(sideloadlist)) {
                                    JSONArray jsonArray = new JSONArray(sideloadlist);
                                    builder.append(jsonArray);
                                } else {
                                    String packageID = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_PACKAGE_ID");
                                    String source = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_SOURCE");
                                    builder.append("PackageID: ").append(packageID);
                                    builder.append("Source Install: ").append(source);
                                }

                            } catch (Exception e) {
                                e.fillInStackTrace();
                            }
                            Utility.sendEventEmitter(reactContext, VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED, builder.toString(), VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_DEVELOPER_OPTIONS_ENABLED: {
                            Utility.sendEventEmitter(reactContext, VGUARD_DEVELOPER_OPTIONS_ENABLED, null, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_AIRDROID_PORT_IS_OPEN: {
                            Utility.sendEventEmitter(reactContext, VGUARD_AIRDROID_PORT_IS_OPEN, null, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_VIRTUAL_TAP_DETECTED: {
                            VGVirtualTapType type = (VGVirtualTapType) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_TYPE);
                            String virtualTapTypeString = null;
                            if(type != null) {
                                virtualTapTypeString = Utility.convertVirtualTapTypeToString(type);
                            }
                            Utility.sendEventEmitter(reactContext, VGUARD_VIRTUAL_TAP_DETECTED, virtualTapTypeString, VGUARD_EVENTS);
                            break;
                        }
                        case VGUARD_NETWORK_DETECTED: {
                            VGuardNetworkType[] networkTypes = (VGuardNetworkType[]) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_NETWORK_TYPES);
                            if(networkTypes != null) {
                                WritableArray arrayData = Utility.getArrayNetworkTypes(networkTypes);
                                Utility.sendEventEmitter(reactContext, VGUARD_NETWORK_DETECTED, arrayData, VGUARD_EVENTS);
                            }
                            break;
                        }
                    }
                }
            }
        };
    }

    private BroadcastReceiver mVtapRcvr;
    private BroadcastReceiver initializeVTapReceiver() {
        return new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();

                if (VTAP_SETUP_ACTION.equalsIgnoreCase(action)) {
                    vtapSetupStatus = intent.getIntExtra(VTAP_SETUP_STATUS, 0);

                    if (vtapSetupStatus == VTAP_SETUP_SUCCESS) {
                        Utility.sendEventEmitter(reactContext, VTAP_SETUP_ACTION, VTAP_SETUP_SUCCESS, VTAP_EVENTS);
                    } else {
                        iVTapManager = VTapFactory.getInstance(reactContext);
                        //Handle error 40207
                        if(vtapSetupStatus == 40207) {
                            //Remove all tokens
                            String[] allTokens = iVTapManager.getAllProvisionedTokens();
                            for (String token : allTokens) {
                                iVTapManager.removeTokenFirmware(token);
                            }
                            iVTapManager.resetVOSTrustedStorage();
                            iVTapManager.onDestroy();
                            iVTapManager = null;
                            setupVTap();
                        } else {
                            //Handle error 40204
                            if(vtapSetupStatus == 40204){
                                //Remove all tokens
                                String[] allTokens = iVTapManager.getAllProvisionedTokens();
                                for (String token : allTokens) {
                                    iVTapManager.removeTokenFirmware(token);
                                }
                            }
                            Utility.sendEventEmitter(reactContext, VTAP_SETUP_ACTION, vtapSetupStatus, VTAP_EVENTS);
                        }
                    }
                }
            }
        };
    }

    private void quitApp() {
        ActivityManager am = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.AppTask> appTaskList = am.getAppTasks();
        if (appTaskList != null && !appTaskList.isEmpty()) {
            ActivityManager.AppTask appTask = appTaskList.get(0);
            appTask.finishAndRemoveTask();
            Handler handler = new Handler();
            handler.postDelayed(() -> {
                try {
                    android.os.Process.killProcess(android.os.Process.myPid());
                } catch (Exception ignored) {}
            }, 600);
        }
    }

    private WritableArray getArrayThreats(Intent intent) {
        ArrayList<Parcelable> detectedThreats = intent.getParcelableArrayListExtra(SCAN_COMPLETE_RESULT);
        StringBuilder builder = new StringBuilder();
        WritableArray arrayData = Arguments.createArray();
        if(detectedThreats != null) {
            for (Parcelable info : detectedThreats) {
                BasicThreatInfo threatInfo = (BasicThreatInfo) info;
                WritableMap infoMap = Arguments.createMap();
                infoMap.putString("ThreatClass", threatInfo.getThreatClass());
                infoMap.putString("ThreatInfo", threatInfo.getThreatInfo());
                infoMap.putString("ThreatName", threatInfo.getThreatName());
                infoMap.putString("ThreatPackageID", threatInfo.getThreatPackage());

                arrayData.pushMap(infoMap);

                // print log
                String infoStr = info.toString();
                builder.append(infoStr).append("\n");
            }
        }
        return arrayData;
    }
    private void handleSslErrorDetection(Intent intent, String eventName) {
        boolean sslErr = intent.getBooleanExtra(VGUARD_SSL_ERROR_DETECTED, false);

        WritableMap mapData = Arguments.createMap();
        mapData.putBoolean(VGUARD_SSL_ERROR_DETECTED, sslErr);
        if (sslErr) {
            try {
                String message = intent.getStringExtra(VGUARD_MESSAGE);
                if (message != null) {
                    JSONObject jsonObject = new JSONObject(message);
                    mapData.putString(VGUARD_ALERT_TITLE, jsonObject.optString(VGUARD_ALERT_TITLE));
                    mapData.putString(VGUARD_ALERT_MESSAGE, jsonObject.optString(VGUARD_ALERT_MESSAGE));
                }
            } catch (Exception ignored) {
            }
        }
        Utility.sendEventEmitter(reactContext, eventName, mapData, VGUARD_EVENTS);
    }

    private void handleThreatPolicy(Intent intent, String eventName) {
        WritableMap mapData = Arguments.createMap();
        WritableArray arrayResponses = getArrayResponses(intent);

        mapData.putArray("responses", arrayResponses);

        VGThreatPolicy highestResponse;
        if(intent.hasExtra(VGUARD_HIGHEST_THREAT_POLICY)){
            highestResponse = (VGThreatPolicy) intent.getSerializableExtra(VGUARD_HIGHEST_THREAT_POLICY);
        } else {
            highestResponse = highestThreatPolicy;
            highestThreatPolicy = null;
        }
        String highestResponseString = Utility.convertThreatPolicyToString(highestResponse);
        mapData.putString(VGUARD_HIGHEST_THREAT_POLICY, highestResponseString);

        String alertTitle = "";
        if(intent.hasExtra(VGUARD_ALERT_TITLE)) {
            alertTitle = intent.getStringExtra(VGUARD_ALERT_TITLE);
            mapData.putString(VGUARD_ALERT_TITLE, alertTitle);
        }
        String alertMsg = "";
        if(intent.hasExtra(VGUARD_ALERT_MESSAGE)) {
            alertMsg = intent.getStringExtra(VGUARD_ALERT_MESSAGE);
            mapData.putString(VGUARD_ALERT_MESSAGE, alertMsg);
        }
        long disabledAppExpired = intent.getLongExtra(VGUARD_DISABLED_APP_EXPIRED, 0);
        mapData.putInt(VGUARD_DISABLED_APP_EXPIRED, (int) disabledAppExpired);

        StringBuilder builder = new StringBuilder();
        if (highestResponse != BY_PASS) {
            builder.append("highest policy: ").append(highestResponseString).append("\n");
        }
        if (!TextUtils.isEmpty(alertTitle)) {
            builder.append("alertTitle: ").append(alertTitle).append("\n");
        }
        if (!TextUtils.isEmpty(alertMsg)) {
            builder.append("alertMsg: ").append(alertMsg).append("\n");
        }
        if (disabledAppExpired > 0) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String activeDate = format.format(new Date(disabledAppExpired));
            builder.append("App can use again after: ").append(activeDate).append("\n");
        }

        Utility.sendEventEmitter(reactContext, eventName, mapData, VGUARD_EVENTS);
    }

    private WritableArray getArrayResponses(Intent intent) {
        ArrayList<VGThreatResponse> threatResponses = intent.getParcelableArrayListExtra(SCAN_COMPLETE_RESULT);
        StringBuilder builder = new StringBuilder();
        WritableArray arrayData = Arguments.createArray();
        if(threatResponses != null) {
            for (VGThreatResponse response : threatResponses) {
                WritableMap infoMap = Arguments.createMap();
                infoMap.putString("title", response.getTitle());
                infoMap.putString("message", response.getMessage());
                infoMap.putString("categoryName", response.getCategoryName());
                infoMap.putString("categoryValue", response.getCategoryValue());
                infoMap.putString("threatPolicy", Utility.convertThreatPolicyToString(response.getThreatPolicy()));
                if(highestThreatPolicy  == null) {
                    highestThreatPolicy = response.getThreatPolicy();
                }
                if(dialogTitle.isEmpty()){
                    dialogTitle = response.getTitle();
                }
                if(dialogMessage.isEmpty()){
                    dialogMessage = response.getFormattedMessage();
                }
                if(!dialogThreats.contains(response)){
                    dialogThreats.add(response);
                }
                arrayData.pushMap(infoMap);

                // print log
                String infoStr = response.toString();
                builder.append(infoStr).append("\n");
            }
        }
        return arrayData;
    }

    public static String toSecondsString(long millis) {
        return String.format(Locale.US, "%.3f", millis / 1000.0);
    }
}
