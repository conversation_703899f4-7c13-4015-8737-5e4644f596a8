package com.vkey.vtap.common;

import android.content.ContentValues;
import android.util.Base64;

import androidx.annotation.Nullable;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableMapKeySetIterator;
import com.facebook.react.bridge.ReadableType;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.vkey.android.vguard.model.VGThreatPolicy;
import com.vkey.android.vguard.model.VGVirtualTapType;
import com.vkey.android.vguard.model.VGuardNetworkType;

import java.util.ArrayList;
import java.util.List;

public class Utility {
    private static final char[] hexArray = "0123456789abcdef".toCharArray();

    public static String decryptApin(String value) {
        String apin = value;
        if ((value.length() == 88) || (value.length() == 108)) {

            byte[] data = Base64.decode(apin, Base64.DEFAULT);
            apin = Utility.bytesToHex(data);
        }
        return apin;
    }
    private static char[] encodeHexChar(byte[] data) {
        char[] hexChars = new char[data.length * 2];
        int v;
        for (int j = 0; j < data.length; j++) {
            v = data[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return hexChars;
    }

    public static String encodeHex(byte[] data) {
        if(data != null) {
            char[] enHex = encodeHexChar(data);
            return new String(enHex);
        }
        return null;
    }

    public static String bytesToHex(byte[] data) {
        if (data == null) {
            return null;
        }

        int len = data.length;
        StringBuilder str = new StringBuilder();
        for (byte datum : data) {
            if ((datum & 0xFF) < 16)
                str.append("0").append(Integer.toHexString(datum & 0xFF));
            else
                str.append(Integer.toHexString(datum & 0xFF));
        }
        return str.toString();
    }

    public static byte[] hexToBytes(final String strHex) {
        if(strHex == null) return null;
        char[] hex = strHex.toCharArray();
        int length = hex.length / 2;
        byte[] raw = new byte[length];
        for (int i = 0; i < length; i++) {
            int high = Character.digit(hex[i * 2], 16);
            int low = Character.digit(hex[i * 2 + 1], 16);
            int value = (high << 4) | low;
            if (value > 127)
                value -= 256;
            raw[i] = (byte) value;
        }
        return raw;
    }

    public static WritableArray byteArrayToWritableArray(byte[] byteArray) {
        WritableArray writableArray = Arguments.createArray();
        for (byte b : byteArray) {
            // Convert byte to unsigned int (0–255) before pushing
            writableArray.pushInt(b & 0xFF);
        }
        return writableArray;
    }

    public static byte[] readableArrayToByteArray(ReadableArray array) {
        byte[] bytes = new byte[array.size()];
        for (int i = 0; i < array.size(); i++) {
            bytes[i] = (byte) array.getInt(i);
        }
        return bytes;
    }

    public static WritableArray toWritableArray(@Nullable String[] values) {
        WritableArray resultArray = Arguments.createArray();
        if (values != null) {
            for (String value : values) {
                resultArray.pushString(value);
            }
        }
        return resultArray;
    }

    public static WritableArray convertToWritableArray(ArrayList<String> values) {
        WritableArray arrayData = Arguments.createArray();
        for (String item : values) {
            arrayData.pushString(item);
        }

        return arrayData;
    }
    public static ArrayList<String> toArrayListString(@Nullable ReadableArray readableArray) {
        if (readableArray == null) {
            return null;
        }

        ArrayList<String> result = new ArrayList<>(readableArray.size());
        for (int index = 0; index < readableArray.size(); index++) {
            ReadableType type = readableArray.getType(index);
            switch (type) {
                case String:
                    result.add(readableArray.getString(index));
                    break;
                case Number:
                    // Use getDouble to handle both int and double values
                    result.add(String.valueOf(readableArray.getDouble(index)));
                    break;
                case Boolean:
                    result.add(readableArray.getBoolean(index) ? "1" : "0");
                    break;
                case Null:
                    result.add(null);
                    break;
                default:
                    // For Map and Array types, you might want to handle them differently
                    result.add(readableArray.getDynamic(index).toString());
                    break;
            }
        }

        return result;
    }


    public static String convertThreatPolicyToString(VGThreatPolicy highestResponse) {
        if(highestResponse != null) {
            return switch (highestResponse) {
                case BY_PASS -> "BY_PASS";
                case ALERT_USER -> "ALERT_USER";
                case QUIT_APP -> "QUIT_APP";
                case DISABLE_APP -> "DISABLE_APP";
                case BLOCK_NETWORK -> "BLOCK_NETWORK";
            };
        }
        return "";
    }

    public static String convertVirtualTapTypeToString(VGVirtualTapType type) {
        return switch (type) {
            case VIRTUAL_TAP -> "VIRTUAL_TAP";
            case CORELLIUM_VD -> "CORELLIUM_VD";
            case ANDROID_STUDIO_EMULATOR -> "ANDROID_STUDIO_EMULATOR";
            case AIRDROID_USB_DEBUG -> "AIRDROID_USB_DEBUG";
            case P_CLOUDY_NON_VIRTUAL -> "P_CLOUDY_NON_VIRTUAL";
            case ANDROID_STUDIO_MIRRORING -> "ANDROID_STUDIO_MIRRORING";
        };
    }

     public static WritableArray getArrayNetworkTypes(VGuardNetworkType[] networkTypes) {
        WritableArray arrayData = Arguments.createArray();
        for(VGuardNetworkType networkType: networkTypes) {
            String networkTypeString = convertNetworkTypeToString(networkType);
            arrayData.pushString(networkTypeString);
        }
        return arrayData;
    }

    public static String convertNetworkTypeToString(VGuardNetworkType networkType) {
        return switch (networkType) {
            case WIFI -> "WIFI";
            case CELLULAR -> "CELLULAR";
            case VPN -> "VPN";
            case ETHERNET -> "ETHERNET";
            case BLUETOOTH -> "BLUETOOTH";
            case WIFI_AWARE -> "WIFI_AWARE";
            case LOWPAN -> "LOWPAN";
            case USB -> "USB";
            case THREAD -> "THREAD";
            case SATELLITE -> "SATELLITE";
        };
    }

    public static void sendEventEmitter(ReactApplicationContext reactAppCtx, String eventName, @Nullable Object params, String component) {
        WritableMap emitData = Arguments.createMap();
        emitData.putString("action", eventName);
        String key = "data";
        if (params != null) {
            if (params instanceof Integer) {
                emitData.putInt(key, (Integer) params);
            } else if (params instanceof Long) {
                emitData.putString(key, Long.toString((long) params));
            } else if (params instanceof String) {
                emitData.putString(key, (String) params);
            } else if (params instanceof WritableArray) {
                emitData.putArray(key, (WritableArray) params);
            } else if (params instanceof WritableMap) {
                emitData.putMap(key, (WritableMap) params);
            }
        }

        reactAppCtx
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(component, emitData);
    }

    public static List<ContentValues> convertReadableArrayToContentValuesList(ReadableArray readableArray) {
        List<ContentValues> contentValuesList = new ArrayList<>();

        for (int i = 0; i < readableArray.size(); i++) {
            ReadableMap readableMap = readableArray.getMap(i);
            ContentValues contentValues = new ContentValues();

            ReadableMapKeySetIterator iterator = readableMap.keySetIterator();
            while (iterator.hasNextKey()) {
                String key = iterator.nextKey();
                ReadableType type = readableMap.getType(key);

                switch (type) {
                    case String:
                        contentValues.put(key, readableMap.getString(key));
                        break;
                    case Number:
                        // React Native represents all numbers as double
                        contentValues.put(key, readableMap.getDouble(key));
                        break;
                    case Boolean:
                        contentValues.put(key, readableMap.getBoolean(key));
                        break;
                    case Null:
                        contentValues.putNull(key);
                        break;
                    default:
                        // Handle other types if necessary
                        break;
                }
            }

            contentValuesList.add(contentValues);
        }

        return contentValuesList;
    }
}
