package com.vkey.vtap.task;

import android.os.Handler;
import android.os.Looper;

import com.facebook.react.bridge.Promise;
import com.vkey.android.vtap.VTapInterface;

import java.util.ArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ProvisioningTask {
    public Promise promise;
    public VTapInterface iVTapManager;
    ArrayList<String> provisioningInfo;
    boolean isHttpPost;

    public ProvisioningTask(ArrayList<String> provisioningInfo, boolean isHttpPost) {
        this.provisioningInfo = provisioningInfo;
        this.isHttpPost = isHttpPost;
    }

    public void execute() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Handler handler = new Handler(Looper.getMainLooper());

        executor.execute(() -> {
            int getTokenFirmwareStatus;
            if(isHttpPost) {
                getTokenFirmwareStatus = iVTapManager.getLoadAckTokenFirmware(provisioningInfo, isHttpPost);
            }
            else {
                getTokenFirmwareStatus = iVTapManager.getLoadAckTokenFirmware(provisioningInfo);
            }
            handler.post(() -> {
                promise.resolve(getTokenFirmwareStatus);
            });
        });
    }

}
