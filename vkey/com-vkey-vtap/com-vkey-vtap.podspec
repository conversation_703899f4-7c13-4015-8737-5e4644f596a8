require "json"

package = JSON.parse(File.read(File.join(__dir__, "package.json")))

Pod::Spec.new do |s|
  s.name = "com-vkey-vtap"
  s.version      = package["version"]
  s.summary      = package["description"]
  s.description  = <<-DESC
                  com-vkey-vtap
                   DESC
  s.homepage     = package['homepage'] || 'htpps://v-key.com'
  s.license      = "MIT"
  # s.license    = { :type => "MIT", :file => "FILE_LICENSE" }
  s.authors      = package["author"] || "V-Key"
  s.platforms    = { :ios => "12.0" }
  s.source       = { :git => "https://github.com/github_account/com-vkey-vtap.git", :tag => "#{s.version}" }

  s.source_files = "ios/*.{h,m}"
  s.requires_arc = true
  s.frameworks  = "UIKit", "CoreGraphics", "Foundation", "MobileCoreServices", "Security", "SystemConfiguration"
  s.libraries = "sqlite3", "z", "c++"
  s.compiler_flags = '-ObjC'

  s.vendored_frameworks = 'ios/*.xcframework'
  # s.vendored_frameworks = [
  #   "ios/cryptota.xcframework",
  #   "ios/vguard.xcframework",
  #   "ios/voswrapper.xcframework"
  # ]
  s.public_header_files = 'ios/*.h'
  s.static_framework = true
  
  s.dependency "React"
  # ...
  # s.dependency "..."
end