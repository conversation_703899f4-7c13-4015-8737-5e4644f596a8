//
//  Utils.m
//  RNVosSmarttokenReactNative
//
//  Created by <PERSON><PERSON><PERSON> (An) on 6/13/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

#import "Utils.h"

@implementation Utils

#define ERROR           -1

+(NSError*)convertToError:(NSException *)exception {
    NSMutableDictionary * info = [NSMutableDictionary dictionary];
    [info setValue:exception.name forKey:@"ExceptionName"];
    [info setValue:exception.reason forKey:@"ExceptionReason"];
    [info setValue:exception.userInfo forKey:@"ExceptionUserInfo"];
    
    NSError *error = [[NSError alloc] initWithDomain:@"" code:ERROR userInfo:info];
    return error;
}


@end
