#pragma once

unsigned char* getVersion(void* vmhandle);
int processManifest(void* vmhandle, char* activationPin, int length, char* filepath);
int StoreUserPin(void* vmhandle,char* pin);
int IsUserPinRegistered(void* vmhandle);
int IsPinRemembered(void* vmhandle);
int checkPin(void* vmhandle, char* pin, bool rememberPin);
int checkPinSeed(void* vmhandle, char* pin, bool rememberPin, int seedNumber);
int changePin(void* vmhandle, char* oldpin, char* newpin);
int generateTotp(void* vmhandle,int* expirytime, int keynum, int otplen);
int generateCR(void* vmhandle, unsigned char* msg, int msglen, int* expirytime, int ocralen);
int generateTxs(void* vmhandle, unsigned char* msg, int msglen, int* expirytime, int ocralen);
unsigned char* getTokenSerial(void* vmhandle);
int initialize(void* vmhandle);
int uninitialize(void* vmhandle);
int loadTA();
int unloadTA();
int loadToken(void* vmhandle, char* tokenSerial);
int unloadToken(void* vmhandle);
//int removeToken(void* vmhandle, char* tokenSerial);
