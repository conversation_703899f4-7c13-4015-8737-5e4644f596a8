//
//  VTapManager.h
//  VTap
//
//  Created by V-Key on 5/3/15.
//  Copyright (c) 2015 vkey. All rights reserved.
//

#import "DistinguishedName.h"
#import "ResultCode.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

/**
 * The types of certificates available.
 */
typedef NS_ENUM(NSInteger, CertType) {
    /**
     * ASP certificate type.
     */
    CERT_TYPE_ASP = 0,

    /**
     * SMP certificate type.
     */
    CERT_TYPE_SMP = 1
};

@protocol VTapManagerDelegate <NSObject>
@required
- (void)didStartVTap:(int)statusCode;
@end

@interface VTapManager : NSObject {
    id<VTapManagerDelegate> delegate;
}
@property (retain) id delegate;

/**
 * This API lets you set whether to automatically send stacktrace information to V-OS App
 * Protection Server when an exception occurred in V-OS App Protection.
 * @param   allowSendStacktraceLog  Whether to allow auto sending of stacktrace information to
 *                                  V-OS App Protection Server, set to `YES` or `NO`. This is set
 *                                  to `No` by default.
 */
- (void)setAllowSendStacktraceLog:(BOOL)allowSendStacktraceLog;

/**
 * This API lets you set whether to automatically send troubleshooting log to V-OS App Protection
 * Server upon app re-launch from a crash. This is enabled by default.
 * @param   allowAutoSendingTroubleshootingLog  Whether to automatically send troubleshooting log
 *                                              to V-OS App Protection Server upon app re-launch
 *                                              from a crash, set to `YES` or `NO`.
 */
- (void)setAllowAutoSendingTroubleshootingLog:(BOOL)allowAutoSendingTroubleshootingLog;

#define PKI_FUNC_ID_AUTH 0
#define PKI_FUNC_ID_VMESSAGE 1
#define PKI_FUNC_ID_DOCSIGN 2

#define ASP_CERT @"ASP_CERT"
#define SMP_CERT @"SMP_CERT"
#define ASP_DOC_CERT @"ASP_DOC_CERT"
#define READ_MSG @"READ_MSG"

#define SEED_TOTP1 1    /*!< The seed number of the first TOTP seed. */
#define SEED_TOTP2 2    /*!< The seed number of the second TOTP seed. */
#define SEED_CR 3       /*!< The seed number for challenge-response. */
#define SEED_TXS 4      /*!< The seed number for transaction signing. */

/*========================= Generic V-Key API Starts =========================*/

// Generic: Set Up V-Key Components API

/**
 * This API gets a `VTapManager` instance that can be used to call the APIs within the `VTapInterface`.
 * @return  A `VTapManager` instance.
 */
+ (VTapManager *)sharedInstance;

/**
 * This API sets the host URL of the V-OS Provisioning Server and V-OS Smart Token Server, respectively.
 * > **Note:** You need to set the host app to call this API before calling other APIs. If the
 * > host app wants to reset the servers in between, `setHostName` can be called again to replace
 * > the URLs of the old servers.
 * @param   provServer  The host URL of the V-OS Provisioning Server.
 * @param   vtapServer  The host URL of the V-OS Smart Token Server.
 */
- (void)setHostName:(NSString *)provServer vtapServer:(NSString *)vtapServer;

/**
 * set Server URL for ThreatIntelligence feature
 *
 * Parameters: server URL to connect
 */
- (void)setThreatIntelligenceServerURL:(NSString *)serverURL;

/**
 * This API gets an integer status code value to the implemented delegate method `didStartVTap`
 * stating the status of the V-OS Smart Token startup process.
 *
 * This API needs to be called in the splash screen of the host app with a progress dialog to
 * load the necessary V-OS Smart Token components such as protecting the app by V-OS App
 * Protection against threats, loading token, etc.
 *
 * V-OS Smart Token setup does the following:
 *
 * - Starts V-OS & V-OS App Protection.
 * - Sets the trusted time server (from V-OS Smart Token Server).
 * - Sends troubleshooting log to V-OS App Protection Server if there is an app crash previously.
 * - If an old token is already provisioned, this API moves the corresponding files as per new
 * format (in the `/TS/` folder), assign it as the default token, and add it in the provisioned
 * token list.
 * - Loads and initializes firmware if provisioning is done.
 *
 * Implement the delegate method as below:
 *
 * ```objectivec
 * //singleton instance
 * _vtapMgr = [VTapManager sharedInstance];
 *
 * //set instance's delegate value
 * _vtapMgr.delegate = self;
 *
 * //call setHostName
 * [_vtapMgr setHostName:PROVISIONING_SERVER_URL vtapServer:VTAP_SERVER_URL];
 *
 * @protocol VTapManagerDelegate <NSObject>
 * @required
 * (void) didStartVTap:(int)statusCode;
 * @end
 * ```
 */
- (void)setupVTap;

/**
 * This API gets an integer status code value to the implemented delegate method `didStartVTap`
 * stating the status of the V-OS Smart Token startup process.
 *
 * This API is specifically for the condition where V-OS has already started with VGuard.
 */
- (void)setupVTap:(BOOL)isVKeyExisting;

/**
 * This API gets the Device Finger Print (DFP) hash of the device.
 * @return  The DFP hash string, e.g. `02edf...b3103`.
 */
- (NSString *)getDFPHash;

/**
 * This API checks whether the firmware is provisioned in the V-OS Smart Token SDK for you to
 * decide whether to do provisioning or skip the provisioning process.
 * @return  Returns `YES` if the provisioning is done.
 *          Returns `NO` if the provisioning is not done.
 */
- (BOOL)isProvisioningDone;

/**
 * This API gets the token serials of all the tokens that are already provisioned in the host app.
 * @return  The array of token serials of all the tokens, e.g. `["rXF7343347", "KWC4CAFF3D",
 *          "UHEDFA7AAE"]`
 */
- (NSArray *)getAllProvisionedTokens;

//-(int)setDefaultToken:(NSString *)TokenSerial;
//-(NSString *)getDefaultToken;

/**
 * This API gets the type of token using token serial and does more format validations.
 * > **Note:** A simpler way is to identify using the first character in the token serial. OTP
 * > TA token serial starts with upper case alphabet PKI TA token serial starts with lower case
 * > alphabet.
 * @param   tokenSerial The token serial.
 * @return              This API returns one of the following integer.
 *                      - `2`: `OTP_TOKEN`
 *                      - `3`: `PKI_TOKEN`
 *                      - `4`: `UNKNOWN_TOKEN`
 */
- (TokenType)getTokenType:(NSString *)tokenSerial;


/**
 * This API clears provisioned tokens & V-OS trusted storage
 * @return  Returns `YES` if the process is done.
 *          Returns `NO` if the process is not done.
 */
- (BOOL)resetVOSTrustedStorage;


// Generic : Lifecycle Actions API

/**
 * Whenever the host app resumes from the background, the host app should call `onResume` API
 * to do initialization of firmware and threat scanning by V-OS App Protection.
 */
- (void)onResume;

/**
 * Whenever the host app goes to the background, the host app should call `onPause` API to do
 * un-initialization of firmware.
 */
- (void)onPause;

/**
 * Whenever the host app exits, the host app should call `onDestroy` API to do unloading of
 * tokens and shutting down of V-OS.
 * > **Note:** Make sure that `onDestroy` is only called when the host app no longer needs V-OS
 * > Token APIs, or on app exit. If `onDestroy` is called on every activity exit, the V-OS will
 * > be shut down and other APIs will not work as expected in the next activity.
 */
- (void)onDestroy;

// Generic: Troubleshooting Logs API

/**
 * This API gets the device-specific troubleshooting ID of the V-OS Smart Token SDK.
 * @return  The troubleshooting ID, e.g. `98c77...1b6cf`.
 */
- (NSString *)getTroubleshootingId;

/**
 * This API sends the troubleshooting logs of V-OS Smart Token and V-OS App Protection to the
 * V-OS Smart Token Server. You can get the troubleshooting logs from the V-OS App Protection
 * Server using the troubleshooting ID.
 * @return  This API returns one of the result code integers.
 * @return  [Success: 40502 | Failed: 40503, 41012]
 */
- (int)sendTroubleshootingLogs DEPRECATED_MSG_ATTRIBUTE("This API will be removed in the next version. Use TLA Logging feature.");

//  Generic: Device Check API

/**
 * This API gets the device model and checks the model against an approved list of devices on
 * the V-OS Smart Token Server. This API returns a response integer stating the device check status.
 * @return  This API returns one of the result code integers.
 * @return  [Success: 40300, 40301, 40302 | Failed: 40303, 41012]
 */
- (int)checkDeviceCompatibility DEPRECATED_MSG_ATTRIBUTE("This API is deprecated and it will be removed in the next version.");

/**
 * You need to call this API after the `checkDeviceCompatibility` API returns `40302 VTAP_GREY_LISTED_DEVICE`.
 * This API sends the device information to the V-OS Smart Token Server for statistical analysis.
 * Set the `status` parameter to `1` if the app user wants to continue running the app at his/her
 * own risk.
 * @param  deviceInfo   Set to `greylist`.
 * @param  status       Set to `1` if the app user wants to continue running the app at his/her
 *                      own risk. This can be one of the following values but only `1` is allowed
 *                      from the host app. Other calls are handled by the SDK automatically:
 *                      - `Status = 0`: Send from the SDK on during device compatibility check
 *                      - `Status = 1`: Send from the host app after user wishes to continue at
 *                      their own risk for greylisted device
 *                      - `Status = 2`: Send from the SDK by the `loadTokenFirmware` API after
 *                      provisioning is successfully done for the greylisted device
 *                      - `Status = 3`: Send from the SDK by the `loadTokenFirmware` API when
 *                      provisioning is not successful on the greylisted device
 * @return  This API returns one of the result code integers.
 * @return              [Success: 40400 | Failed: 40401, 41012]
 */
- (int)sendDeviceInfo:(NSString *)deviceInfo status:(int)status DEPRECATED_MSG_ATTRIBUTE("This API is deprecated and it will be removed in the next version.");

/**
 * This API gets the trusted time, through V-OS, which obtained from the V-OS Smart Token Server.
 * @return  On success, this API returns the trusted time timestamp, e.g. `1524729263`. The
 *          success return should be positive number except `42031` which is
 *          `VTAP_TRUSTED_TIME_TIMEOUT` response code.
 * @return  This API returns one of the result code integers.
 * @return  [Failed: 42031, -1100, -1101, -1102, -1103, -1104, -1105, -1106, -1107, -1108,
 *          -1109, -1110, -1111, -10301, -10302, -10303, -10304, -10305, -10306, -10307]
 */
- (int)getTrustedTime;

/**
 * This API returns the full version number of V-OS Smart Token SDK.
 * @return  This API returns the version number in the `<VERSION>(<GIT_HASH>)`
 *          format, e.g. `4.7.10.1234(abc123de)`. When it fails to retrieve the
 *          version number, it returns `NA` instead.
 */
- (NSString *)sdkVersion;


/**
 * This API overrides the default timeout interval of 15 seconds
 * to the specified value if the value specified is in between 15 & 60.
 *
 */
- (void)setTimeout:(int)timeoutInterval;

/**
* This API returns the timeout interval - either specified or default.
*
*/
- (int)getTimeout;

/*========================= Generic VKEY API Ends =========================*/

// Luke Pham added for new rest APIs

/**
 * This API sets the additional data value is to be included in `additionaldata` parameter in
 * designated RESTful APIs.
 * @param   data        The data string.
 * @param   dataType    The available data type are as follows: `TYPE_REGISTER_PUSH_NOTIFICATION`,
 *                      `TYPE_AUTHENTICATION`, `TYPE_VMESSAGE`, `TYPE_CERTIFICATE_AUTH`,
 *                      `TYPE_CERTIFICATE_SMP`, `TYPE_CERTIFICATE_AUTH_RENEW`,
 *                      `TYPE_CERTIFICATE_SMP_RENEW`.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41020 | Failed: 41018]
 */
- (int)setAdditionalData:(NSString *)data additionalDataType:(NSString *)dataType;

/**
 * This API sets the message notification type required by the additional data mechanism.
 * This API is normally put in the receiver of the push notifications.
 * @param   data    The string value from `MESSAGE_TYPE` received from the push notification.
 * @return          This API returns one of the result code integers.
 * @return          [Success: 41020 | Failed: 41018]
 */
- (int)setMessageTypeData:(NSString *)data;

/**
 * This API gets the additional data.
 * @param   dataType    The available data type are as follows: `TYPE_REGISTER_PUSH_NOTIFICATION`,
 *                      `TYPE_AUTHENTICATION`, `TYPE_VMESSAGE`, `TYPE_CERTIFICATE_AUTH`,
 *                      `TYPE_CERTIFICATE_SMP`, `TYPE_CERTIFICATE_AUTH_RENEW`,
 *                      `TYPE_CERTIFICATE_SMP_RENEW`.
 * @return              This API returns the additional data in string format.
 */
- (NSString *)getAdditionalData:(NSString *)dataType;

/**
 * This API clears the additional data set in a particular data type.
 * @param   dataType    The available data type are as follows: `TYPE_REGISTER_PUSH_NOTIFICATION`,
 *                      `TYPE_AUTHENTICATION`, `TYPE_VMESSAGE`, `TYPE_CERTIFICATE_AUTH`,
 *                      `TYPE_CERTIFICATE_SMP`, `TYPE_CERTIFICATE_AUTH_RENEW`,
 *                      `TYPE_CERTIFICATE_SMP_RENEW`
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41019 | Failed: 41018]
 */
- (int)disableAdditionalData:(NSString *)dataType;

/*========================= OTP TA API Starts =========================*/

// OTP TA: Transaction API

/**
 * This API sets the length of OTP in the range between 6 to 8.
 * @param   length  The length of OTP, `6`, `7`, or `8`.
 * @return          This API returns one of the result code integers.
 * @return          [Success: 41000 | Failed: 41001]
 */
- (int)setOtpLength:(int)length;

/**
 * This API generates an TOTP and its validity (in seconds).
 * @param   num The respective number of the type of TOTP to be generated:
 *              - `1` – OTP for login
 *              - `2` – OTP for transaction
 * @return      On success, this API returns an array with the TOTP at index position `0` and
 *              time to expiration (in seconds) at index position `1`, e.g. `["********", 30]`.
 *              Otherwise, this API returns one of the result code integers at index position `0`,
 *              e.g. `[41002]`.
 * @return      [Failed: 41002, 41003, 41008, 41009, 40802, 41010]
 */
- (NSArray *)generateTOTP:(int)num;

/**
 * For transactions that are deemed risky, such as fund transfers, the app user can generate a
 * challenge-response value using the V-OS Smart Token SDK. The challenge-response value is an
 * OTP, which the app user can use, in his/her secure transactions.
 * @param   crMsg   The message used for challenge-response, e.g. 100000 (transaction amount).
 *                  The length of the message should be <=40.
 * @return          On success, this API generates challenge-response and returns an array with the
 *                  CR at index position `0` and time to expiration (in seconds) at index position
 *                  `1`, e.g. `["********", 30]`. Otherwise, this API returns one of the result code
 *                  integers at index position `0`, e.g. `[41004]`.
 * @return          [Failed: 41004, 41005, 41008, 41009, 40802, 41010]
 */
- (NSArray *)generateCR:(NSString *)crMsg;

/**
 * The app user can generate a transaction signing value to authenticate secure transactions.
 * The transaction signing value is an OTP, which the app user can use, in his/her secure
 * transactions.
 * @param   account The message used for transaction signing, e.g. 6736483 (transaction account).
 *                  The length of the `account` and `amount` combined should be <=40.
 * @param   amount  The message used for transaction signing, e.g. 100000 (transaction amount).
 *                  The length of the `account` and `amount` combined should be <=40.
 * @return          On success, this API generates transaction signing value and returns an array
 *                  with the transaction OTP at index position `0` and time to expiration (in
 *                  seconds) at index position `1`, e.g. `["********", 30]`. Otherwise, this API
 *                  returns one of the result code integers at index position `0`, e.g. `[41006]`.
 * @return          [Failed: 41006, 41007, 41008, 41009, 40802, 41010]
 */
- (NSArray *)generateTxS:(NSString *)account amount:(NSString *)amount;

/**
 * This API gets the token serial of currently loaded OTP token.
 * > **Note:** The host app can only call this API after the provisioning is done.
 * @return  The token serial string, e.g. `mT94CE4629`.
 */
- (NSString *)getTokenSerial;

/*========================= OTP TA API Ends =========================*/

/*========================= Multiple TA API Starts =========================*/

// Multiple(OTP/PKI) TA: Provisioning API

/**
 * This API validates the token serial and activation PIN checksum and returns the boolean
 * value `YES` or `NO`.
 * @param   provisioningInfo    The array consists of the token serial and activation PIN:
 *                              - Index `0` = token serial
 *                              - Index `1` = activation PIN
 * @return                      This API returns `YES` if the checksum validation passes or `NO` if
 *                              the checksum validation fails.
 */
- (BOOL)validateChecksum:(NSArray *)provisioningInfo;

/**
 * This API downloads the token firmware from the V-OS Provisioning Server, unzips the
 * downloaded (ZIP file) token firmware in the device, and loads the firmware in the device and
 * processes the TA manifest. This API also sends an acknowledgment to V-OS Provisioning Server.
 * > **Note:** This API is used when the token APIN stored in the V-OS TMS is in plain form.
 * @param   provisioningInfo    The array consists of the token serial and activation PIN:
 *                              - Index `0` = token serial
 *                              - Index `1` = activation PIN
 * @return                      This API returns one of the result code integers.
 * @return                      [Success: 40600 | Failed: 40601, 40602, 40603, 40604, 40605, 40606,
 *                              40607, 41012]
 */
- (int)getLoadAckTokenFirmware:(NSArray *)provisioningInfo;

/**
 * This API downloads the token firmware from the V-OS Provisioning Server, unzips the
 * downloaded (ZIP file) token firmware in the device, and loads the firmware in the device and
 * processes the TA manifest. This API also sends an acknowledgment to V-OS Provisioning Server.
 * You can choose to either send the acknowledgment using HTTP POST or HTTP DELETE method by
 * setting the `isHttpPost` optional parameter.
 * > **Note:** This API is used when the token APIN stored in the V-OS TMS is in plain form.
 * @param   provisioningInfo    The array consists of the token serial and activation PIN:
 *                              - Index `0` = token serial
 *                              - Index `1` = activation PIN
 * @param   isHttpPost          Whether to use HTTP POST method to send acknowledgment to V-OS
 *                              Provisioning Server:
 *                              - `YES`: Use HTTP POST method.
 *                              - `NO`: Use HTTP DELETE method.
 * @return                      This API returns one of the result code integers.
 * @return                      [Success: 40600 | Failed: 40601, 40602, 40603, 40604, 40605, 40606,
 *                              40607, 41012]
 */
- (int)getLoadAckTokenFirmware:(NSArray *)provisioningInfo httpPostMethod:(BOOL)isHttpPost;

/**
 * Besides the `getLoadAckTokenFirmware` API, an alternative approach is that you configure the
 * host app to download the token from the V-OS Provisioning Server and store the token firmware
 * in the internal storage of the device. The downloaded file should be placed in the files
 * directory.
 * > **Note:** If you want to use this API to load the token firmware, the V-OS Seeding Server
 * > is required to configured to generate tokens with encrypted APIN.
 * The host app needs to pass the path of the downloaded token file, together with the token
 * serial and encrypted APIN to this API. This API reads the JSON format token file from the
 * internal storage, creates the ZIP file, unzips the token firmware file, and loads the token
 * firmware in the device and processes the TA manifest.
 *
 * The encrypted APIN should be base64 decoded and converted to hex string before passing to
 * this API as follows:
 * ```objectivec
 * //decode
 * NSData *decodedData;
 * If(SYSTEM_VERSION_GREATER_THEN_OR_EQUAL_TO(@”7.0”))
 *     decodedData = [[NSData alloc] initWithBase64EncodedString:aPIN option:0];
 * else
 *     decodedData = [[NSData alloc] dataFromBase64String:aPIN];
 *
 * //convert to hex
 * aPIN = [decodedData hexadecimalString];
 *
 * //get tokenFirmware
 * int downloadResponse = [self loadTokenFirmware:aPIN downloadFilePath:filePath];
 * ```
 * In the above code snippet, `dataFromBase64String` and `hexadecimalString` are category methods
 * for the class NSData and has the following implementation.
 * ```objectivec
 * + (NSData *)dataFromBase64String:(NSString *)aString {
 *     NSData *data = [aString dataUsingEncoding:NSASCIIStringEncoding];
 *     size_t outputLength;
 *     void *outputBuffer = NewBase64Decode([data bytes], [data length], &outputLength);
 *     NSData *result = [NSData dataWithBytes:outputBuffer length:outputLength];
 *     free(outputBuffer);
 *     return result;
 * }
 * ```
 * ```objectivec
 * //returns hexadecimal string of NSData. Empty string if data is empty.
 * - (NSString *)hexadecimalString {
 *     const unsigned char *dataBuffer = (const unsigned char *)[self bytes];
 *
 *     if (!dataBuffer) return [NSString string];
 *         NSUInteger dataLength = [self length];
 *         NSMutableString *hexString = [NSMutableString stringWithCapacity:(dataLength *2)];
 *
 *     for (int i = 0; i < dataLength; ++i)
 *         [hexString appendString:[NSString stringWithFormat:@"%02lx", (unsigned long)dataBuffer[i]]];
 *
 *     return [NSString stringWithString:hexString];
 * }
 * ```
 * @param   tokenSerial     The token serial.
 * @param   aPIN            The encrypted activation PIN that is base64 decoded and converted to
 *                          hex string.
 * @param   filePath        The path to the downloaded token, e.g. `/downloadedFile/`.
 * @return                  This API returns one of the result code integers.
 * @return                  [Success: 40608 | Failed: 40609, 40610, 40611]
 */
- (int)loadTokenFirmware:(NSString *)tokenSerial APIN:(NSString *)aPIN downloadFilePath:(NSString *)filePath;

/**
 * This API removes the token firmware provisioned in the device based on the token serial.
 * During this process, the SDK un-initializes the TA and unloads the TA from V-OS. It deletes
 * all the token related files like `vtapta.bin`, secure storage files and clears the
 * provisioning status of the specific token serial.
 *
 * If the requested token is a PKI token, the SDK will attempt to call the de-registration web
 * service API of ASP and SMP to remove all the generated key pairs, PKI function registration
 * details, and clears ASP and SMP server data (certificates of respective PKI token serial).
 * Only after the de-registration is successfully executed and the server data is removed, it
 * then proceeds to remove the token firmware.
 * @param   tokenSerial The token serial.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40618 | Failed: 40619]
 */
- (int)removeTokenFirmware:(NSString *)tokenSerial;

/**
 * This API loads an already provisioned token on the device using its token serial. It loads
 * the TA on V-OS and initializes the TA. It also sets the most recently loaded token as
 * default token.
 * @param   tokenSerial The token serial.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40612 | Failed: 40613]
 */
- (int)loadToken:(NSString *)tokenSerial;

/**
 * This API unloads an already loaded token on V-OS using its token serial. It un-initializes
 * the TA and unloads the TA from V-OS.
 * @param   tokenSerial The token serial.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40614 | Failed: 40615]
 */
- (int)unloadToken:(NSString *)tokenSerial;

/**
 * This API gets the version of the V-OS TA token firmware. This API is used by the host app to
 * get the current token version.
 * @param   tokenSerial The token serial.
 * @return              The V-OS TA token firmware version, e.g. `4.8`.
 */
- (NSString *)getTokenFirmwareVersion:(NSString *)tokenSerial;

// Multiple TA: Token PIN Management API

/**
 * After the token is provisioned successfully, the V-OS Smart Token SDK expects a token PIN
 * registration to secure the token. This API checks if token registration is done in V-OS Smart
 * Token SDK for you to decide whether to do create PIN process or skip create PIN process.
 * @param   tokenSerial The token serial.
 * @return              This API returns `YES` if the token PIN is registered or `NO` if the token
 *                      PIN has not yet registered.
 */
- (BOOL)isTokenRegistered:(NSString *)tokenSerial;

/**
 * This API registers a new 6-digit PIN.
 * @param   pin         The 6-digit PIN.
 * @param   tokenSerial The token serial.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40700 | Failed: 40701, 40702, 40802, 41011]
 */
- (int)createTokenPIN:(NSString *)pin withTokenSerial:(NSString *)tokenSerial;

/**
 * This API validates the PIN for the token. If `rememberPin` is set, V-OS Smart Token SDK sets
 * the `rememberPin` flag and stores the hash of the PIN. This API can be used even if `rememberPin`
 * flag is already set just to validate the PIN.
 * > **Note:** You should always call this API after you call `createTokenPIN` to enable OTP
 * > generation. You only need to call this API once per session. During the same session, if
 * > the supplied PIN is correct and successfully validated, you do not need to call this API
 * > again. If you continue to call this API during the same session after successful PIN
 * > validation, this API will always return success result even if the PIN supplied is incorrect.
 * @param   pin             The 6-digit PIN for validation.
 * @param   rememberPin     Whether to remember PIN, set to `YES` or `NO`.
 * @param   tokenSerial     The token serial.
 * @return                  This API returns one of the result code integers.
 * @return                  [Success: 40800 | Failed: 40801, 40802, 41011]
 */
- (int)checkTokenPIN:(NSString *)pin remember:(BOOL)rememberPin withTokenSerial:(NSString *)tokenSerial;

/**
 * This API validates the PIN for the token. If `rememberPin` is set, V-OS Smart Token SDK sets
 * the `rememberPin` flag and stores the hash of the PIN. This API can be used even if `rememberPin`
 * flag is already set just to validate the PIN. You can set the host app to skip PIN verification
 * for using selected seed(s) to generate OTP by setting `rememberPin` and `seed`, respectively.
 * > **Note:** You should always call this API after you call `createTokenPIN` to enable OTP
 * > generation. You only need to call this API once per session. During the same session, if the
 * > supplied PIN is correct and successfully validated, you do not need to call this API again
 * > for OTP generation. If you call this API with an incorrect PIN during the same session after
 * > successful PIN validation, OTP can still be generated. For the high-value transactions, if
 * > you wish to validate the token PIN again before the transaction is performed, unload and
 * > reload the token to reset the check PIN status. The correct PIN will need to be supplied for
 * > PIN validation before OTP can be generated.
 * @param  pin          The 6-digit PIN for validation.
 * @param  rememberPin  Whether to remember PIN, set to `YES` or `NO`.
 * @param  tokenSerial  The token serial.
 * @param  seed         The OTP seed that you want to skip PIN checking.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40800 | Failed: 40801, 40802, 41011]
 */
- (int)checkTokenPIN:(NSString *)pin
            remember:(BOOL)rememberPin
     withTokenSerial:(NSString *)tokenSerial
             andSeed:(int)seed;

/**
 * This API checks the status of the `rememberPin` flag from the V-OS Smart Token SDK. The
 * `rememberPin` flag is for the host app to decide whether to request login or to skip.
 * @param   tokenSerial The token serial.
 * @return              This API returns `YES` if remember PIN is enabled or `NO` if
 *                      remember PIN is not enabled.
 */
- (BOOL)isTokenPINRemembered:(NSString *)tokenSerial;


/**
 * This API validates the old PIN, sets the new PIN, and resets the `rememberPin` flag to `false`.
 * You must let the host app call this API after changing the PIN to set the `rememberPin` flag
 * as preferred, and to enable critical TA APIs.
 * @param  oldPin       The old PIN to be changed.
 * @param  newPin       The new PIN to be changed to.
 * @param  tokenSerial  The token serial.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 40900 | Failed: 40901, 40902, 40802, 41011]
 */
- (int)changeTokenPIN:(NSString *)oldPin withNewPIN:(NSString *)newPin withTokenSerial:(NSString *)tokenSerial;

/*========================= Multiple TA API Ends =========================*/

/*========================= PKI TA API Starts =========================*/

// PKI TA -  Registration

/**
 * This API sets the host URL of the V-OS PKI Token Server.
 * > **Note:** You need to set the host app to call this API before calling other PKI related
 * > APIs. If the host app wants to reset the server in between, `setPKIHostName` can be called
 * > again to replace the URL of the old server.
 * @param pkiServer The host URL of the V-OS PKI Token Server.
 */
- (void)setPKIHostName:(NSString *)pkiServer;

/**
 * This API gets the token serial of currently loaded PKI token.
 * > **Note:** The host app can only call this API after the provisioning is done.
 * @return  The token serial string, e.g. `mT94CE4629`.
 */
- (NSString *)getPKITokenSerial;

// Register APNs Token for Push Notification

/**
 * This API gets the APNS/FCM Push Notification Token and pass it here which will be registered with
 * SMP Server. All the push notifications from V-OS Messaging will be sent to this registered
 * APNS/FCM token.
 * > **Note:** The `userId` and `deviceId` passed in this API will be used for all PKI related
 * > APIs. You can set `userId` and/or `deviceId` as `nil`. By default PKI token serial is stored
 * > as an identifier at the server. e.g. when the host app sends `userId` and push token, the server
 * > will map `userId`, corresponding PKI token serial with push token so you can use token serial
 * > and/or `userId` to send the secure message. When the host app sends only push token, the server
 * > will map only PKI token serial with push token so you can only use token serial to send the
 * > secure message.
 * @param   userId      Any alphanumeric ID, e.g. 97883434. `userId` can be a mobile number,
 *                      national ID, etc. This should be a unique ID representing the user.
 *                      You can send it as `nil`.
 * @param   deviceId    Any alphanumeric ID which uniquely identifies the device. You can set it to
 *                      `nil`.
 * @param   pushToken   The push notification token.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41014 | Failed: 41015, 41011]
 */
- (int)pushNotificationRegister:(NSString *)userId deviceId:(NSString *)deviceId andToken:(NSString *)pushToken;

/**
 * This API gets the APNS/FCM/HMS Push Notification Token and pass it here which will be registered with
 * SMP Server. All the push notifications from V-OS Messaging will be sent to this registered
 * APNS/FCM token.
 * > **Note:** The `userId` and `deviceId` passed in this API will be used for all PKI related
 * > APIs. You can set `userId` and/or `deviceId` as `nil`. By default PKI token serial is stored
 * > as an identifier at the server. e.g. when the host app sends `userId` and push token, the server
 * > will map `userId`, corresponding PKI token serial with push token so you can use token serial
 * > and/or `userId` to send the secure message. When the host app sends only push token, the server
 * > will map only PKI token serial with push token so you can only use token serial to send the
 * > secure message.
 * @param   userId      Any alphanumeric ID, e.g. 97883434. `userId` can be a mobile number,
 *                      national ID, etc. This should be a unique ID representing the user.
 *                      You can send it as `nil`.
 * @param   deviceId    Any alphanumeric ID which uniquely identifies the device. You can set it to
 *                      `nil`.
 * @param   pushToken   The push notification token.
 * @param   pnsType     The push notification service type (e.g. APNs, FCM, HMS).
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41014 | Failed: 41015, 41011]
 */
- (int)pushNotificationRegister:(NSString *)userId deviceId:(NSString *)deviceId andToken:(NSString *)pushToken pnsType:(PNSType)pnsType;

/**
 * This API lets you retrigger the push notification for certificate download if the
 * `generateCsrAndSendSync` API is invoked but the SDK failed to get the certificate.
 * @param   functionID  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41140 | Failed: 41012, 41013, 41131, 41141]
 */
- (int)triggerCertPushNotification:(int)functionID;

/**
 * This API gets the registered PKI `userId`.
 * @return  This API returns the user ID string, e.g. `97883434`.
 */
- (NSString *)getRegisteredUserId;

/**
 * This API gets the registered PKI `deviceId`.
 * @return  This API returns the device ID string, e.g. `235457789`.
 */
- (NSString *)getRegisteredDeviceId;

/**
 * This API generates ECC key pair for the specific function ID using the PKI token and sets
 * the function ID specific PIN which needs to be used by the user to authorize signing
 * (handling is the same as the token PIN). The generation status is sent to the device by
 * push notification server (APNS/FCM). After successful registration and cert download of
 * `functionId` (`0` and `1`), the app user needs to key in the `functionId` PIN to authorize
 * signature generation. There is no PIN required for authorizing the V-OS Messaging decryption.
 * > **Note:** Sequence of CSR generation is important. You must generate CSR for authentication
 * > `fucntionId` first as it must be signed with the pregen key.
 * @param   functionId          The ID of the PKI function:
 *                              - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                              - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @param  distinguishedName    The distinguished name in this format: <b>C=XX,
 *                              ST=XX, L=XX, O=XXX, OU=XXX, givenName=XXX,
 *                              SN=XXX, serialNumber=XXX, CN=XXX/emailAddress=<EMAIL></b>.
 *                              - `C`: country name
 *                              - `ST`: state name
 *                              - `L`: locality name
 *                              - `O`: organization name
 *                              - `OU`: organization unit name
 *                              - `givenName`: given name
 *                              - `SN`: surname
 *                              - `serialNumber`: the serial number of the certificate
 *                              - `CN`: common name
 *                              - `emailAddress`: email address
 * @param   pin                 The PIN. Set this to `0` if the `functionId` is for messaging
 *                              (`PKI_FUNC_ID_V_MESSAGE`), i.e. `functionId` is `1`.
 * @param   rememberPin         Whether to remember PIN, set to `YES` or `NO`.
 * @return                      This API returns one of the result code integers.
 * @return                      [Success: 41100 | Failed: 41101, 41011, 41012, 41013]
 */
- (int)generateCsrAndSend:(int)functionId
        distinguishedName:(DistinguishedName *)distinguishedName
                      pin:(NSString *)pin
           andRememberPin:(BOOL)rememberPin;

/**
 * This API generates ECC key pair for the specific function ID using the PKI
 * token and sets the function ID specific PIN which needs to be used by the
 * user to authorize signing (handling is the same as the token PIN). Different
 * from the generateCsrAndSend API, this API does not rely on push notification
 * server (APNS/FCM) to send the generation status to the device. Instead, this
 * API sends the status and generated data directly to the device. After successful
 * registration and cert download of functionId (0 and 1), the app user needs
 * to key in the functionId PIN to authorize signature generation. There is no
 * PIN required for authorizing the V-OS Messaging decryption.
 * > **Note:** Sequence of CSR generation is important. You must generate
 * CSR for authentication functionId first as it must be signed with the pregen key.
 *
 * > **Note:** This API requires `READ_PHONE_STATE`, `INTERNET`, `ACCESS_NETWORK_STATE`,
 * `ACCESS_WIFI_STATE`, `READ_EXTERNAL_STORAGE`, and `WRITE_EXTERNAL_STORAGE` permissions.
 * @param   functionId          The ID of the PKI function:
 *                              - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                              - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @param  distinguishedName    The distinguished name in this format: <b>C=XX,
 *                              ST=XX, L=XX, O=XXX, OU=XXX, givenName=XXX,
 *                              SN=XXX, serialNumber=XXX, CN=XXX/emailAddress=<EMAIL></b>.
 *                              - `C`: country name
 *                              - `ST`: state name
 *                              - `L`: locality name
 *                              - `O`: organization name
 *                              - `OU`: organization unit name
 *                              - `givenName`: given name
 *                              - `SN`: surname
 *                              - `serialNumber`: the serial number of the certificate
 *                              - `CN`: common name
 *                              - `emailAddress`: email address
 * @param  pin                  The PIN. Set this to `0` if the `functionId` is for messaging
 *                              (`PKI_FUNC_ID_V_MESSAGE`), i.e. `functionId` is `1`.
 * @param  rememberPin          Whether to remember PIN, set to `YES` or `NO`.
 * @return                      [Success: 41100 | Failed: 41101, 41011, 41012, 41013]
 */
- (int)generateCsrAndSendSync:(int)functionId
            distinguishedName:(DistinguishedName *)distinguishedName
                          pin:(NSString *)pin
               andRememberPin:(BOOL)rememberPin;

/**
 * This API downloads the certificate using `messageId` and stores the certificate in the V-OS
 * Smart Token SDK. It also verifies the certificate chain returned by the server and verifies
 * the signature of the certificate. If all the said verifications are successful, then the
 * V-OS Smart Token SDK marks the `functionId` as registered completely. Only after `functionId`
 * completes registration, the `functionId` private key can be used for signing/decrypting.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @param   messageId   The message ID, e.g. a7960bfb-a80c-4be3-a943-48ab46ea89d0.
 * @param   messageType `ASP_Cert` or `SMP_Cert`.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41112 | Failed: 41123, 41011, 41012, 41013, 41129]
 */
- (int)pkiCertDownload:(int)functionId messageId:(NSString *)messageId andMessageType:(NSString *)messageType;

/**
 * This API lets you re-download the PKI certificate when the previous certificate download attempt
 * is not successful from the `generateCsrAndSendSync()` API.
 * @param   functionId  This ID of the function.
 *                      - `0`: authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1`: messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41100 | Failed: 41011, 41012, 41013, 41113, 41123, 41131]
 */
- (int)pkiCertDownloadRetry:(int)functionId;


/**
 * This API gets the Certificate by using CERT_TYPE
 * @return  The Certificate data
 */
- (NSData * _Nonnull)getCert:(CertType)certType;


// PKI PIN Management

/**
 * This API checks if the specified PKI function is registered completely. Any function ID
 * registration is marked completed after successful certificate download and verification
 * process only.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @return              This API returns `YES` if the PKI function ID is registered or `NO` if the
 *                      PKI function ID is not registered.
 */
- (BOOL)isPKIFunctionRegistered:(int)functionId;

/**
 * The API checks if the `rememberPin` flag for the specified function ID is set in TA. This
 * applies only for authentication because V-OS Messaging does not have a PIN.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @return              This API returns `YES` if the PKI function PIN is remembered or `NO` if
 *                      the PKI function PIN is not remembered.
 */
- (BOOL)isPKIFunctionPinRemembered:(int)functionId;

/**
 * This API validates the PIN for the token. If `rememberPin` is set, V-OS Smart Token SDK sets
 * the `rememberPin` flag in the V-OS Smart Token SDK and stores the hash of the PIN. This API
 * can be used even if `rememberPin` flag is already set just to validate the PIN.
 * > **Note:** You should always call this API after you calling any critical TA API like
 * > authentication/decryption. You only need to call this API once per session. During the same
 * > session, if the supplied PIN is correct and successfully validated, you do not need to call
 * > this API again to perform critical TA functions. If you call this API with an incorrect PIN
 * > during the same session after successful PIN validation, critical TA functions can still be
 * > performed. For the high-value transactions, if you wish to validate the token PIN again
 * > before the transaction is performed, unload and reload the token to reset the check PIN
 * > status. The correct PIN will need to be supplied for PIN validation before critical TA
 * > functions can be performed.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @param   pin         The PIN, e.g. `123456`.
 * @param   rememberPin Whether to remember PIN, set to `YES` or `NO`.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41114 | Failed: 40802, 41115, 41011. 41013]
 */
- (int)pkiFunctionCheckPin:(int)functionId withPin:(NSString *)pin andRememberPin:(BOOL)rememberPin;

/**
 * This API validates the old PIN, sets the new PIN and resets remember PIN status to `NO`. Host
 * app must call `checkTokenPIN` API after change PIN operation to set the `rememberPin` flag as
 * preferred and also to enable critical TA APIs.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 * @param   oldPin      The old PIN, e.g. `123456`.
 * @param   newPin      The new PIN, e.g. `789012`.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41116 | Failed: 41117, 41119, 40902, 40802, 41011, 41013]
 */
- (int)pkiFunctionChangePIN:(int)functionId oldPin:(NSString *)oldPin withNewPIN:(NSString *)newPin;

// PKI Signing API

/**
 * This API checks if the user has already registered for authentication signing. During the
 * process, it also generates a signature using the `messageId`, `dataToBeSigned`, `reject`, and
 * various token and device parameters, i.e. token serial, user ID, and device ID. The signature is
 * signed using the ASP private key stored in the V-OS and sent to V-OS PKI Token Server during the
 * request. V-OS PKI Token Server uses this signature to authorize the user.
 * > **Note:** For rejection, the user must provide function PIN if it is not remembered.
 * @param   messageId       The message ID received in the notification.
 * @param   dataToBeSigned  The data to be signed received in the notification.
 * @param   reject          Whether to cancel (`YES`) the notification or to acknowledge (`NO`) the
 *                          notification.
 * @return                  This API returns one of the result code integers.
 * @return                  [Success: 41102 | Failed: 41103, 41105, 41011, 41012, 41013]
 */
- (int)pkiFunctionAuthenticate:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject;

/**
 * @exclude from docs
 */
- (int)pkiFunctionDocSign:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject;

// V-Message API

/**
 * It checks if the user has already registered for V-OS Messaging and decrypts the encrypted
 * content passed to the notification using ECC key pair generated for V-OS Messaging.
 * > **Note:** It checks internally if the user has remembered the token PIN or called check PIN
 * > after the app is launched.
 * @param  encryptedMsg The byte array containing base 64 decoded representation of `notifyMsg`
 *                      received in the V-OS Messaging notification.
 * @return              On success, this API returns the decrypted message in the string format.
 *                      Otherwise, this API returns one of the result codes.
 * @return              [Failed: 41125, 41121, 41011, 41013]
 */
- (NSString *)vMessageDecrypt:(NSData *)encryptedMsg;

/**
 * This API downloads the document or 2-pass message from the server and internally sends an
 * acknowledgment for the notification. It uses `messageType`parameter to differentiate between
 * V-OS PKI Token or V-OS Messaging download request and `messageFlag` indicates whether the
 * content, coming from the server, is encrypted or cleartext. If it is an encrypted message,
 * then V-OS Smart Token SDK will decrypt the message.
 * @param  messageId    The message ID received in the notification.
 * @param  messageType  The message type received in the notification.
 * @param  messageFlag  The msg flag received in the notification.
 * @return              On success, this API returns an array with the document path at index
 *                      position `0` and document type (`pdf` or `xml`) at index position `1`,
 *                      e.g. `["/.../.../.../", "pdf"]`. Otherwise, this API returns one of the
 *                      result code at index position `0`, e.g. `[41111]`.
 * @return              [Failed: 41111, 41011, 41012]
 */
- (NSArray *)vMessageDownload:(NSString *)messageId
                  messageType:(NSString *)messageType
               andMessageFlag:(NSString *)messageFlag;

/**
 * This API sends a push notification acknowledgment to the server after the user reads the
 * notification.
 * @param  messageId    The message ID received in the notification.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 41126 | Failed: 41127, 41012]
 */
- (int)vMessageAck:(NSString *)messageId;

/**
 * This API removes the generated key pair for the `functionId` and deletes the downloaded cert
 * from SDK. If none of AUTH / V-OS Messaging key pair exists, the `userId` will also be removed and
 * deregistered from the server.
 * @param   functionId  The ID of the PKI function:
 *                      - `0` for authentication (`PKI_FUNC_ID_AUTH`)
 *                      - `1` for messaging (`PKI_FUNC_ID_V_MESSAGE`)
 */
- (void)removePKIFunction:(int)functionId;

/*========================= PKI TA API Ends =========================*/

/*========================= Generic PKI API Starts =========================*/

/**
 * This API generates an ECC key pair with the specified ECC key alias (if not exist) and generates
 * the CSR from the corresponding ECC key.
 * @param   inputString     The input string to be embedded in the generated CSR.
 * @param   keyAlias        The alias of the ECC key.
 * @return                  [Success: 42000 | Failed: 42001]
 */
- (NSArray *)generateEccCsrWithAlias:(NSString *)inputString andKeyAlias:(int)keyAlias;

/**
 * This API deletes an ECC key from the keystore.
 * @param   keyAlias    The alias of the ECC key.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42002 | Failed: 42003]
 */
- (int)deleteEccKeyWithAlias:(int)keyAlias;

/**
 * This API signs the input data using the ECC key associated with the specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   keyAlias    The alias of the ECC key.
 * @return              [Success: 42004 | Failed: 42005]
 */
- (NSArray *)eccSignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias;

/**
 * This API hashes the input data using SHA-1 cryptographic hash function and signs the digest
 * using the ECC key associated with the specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   keyAlias    The alias of the ECC key.
 * @return              [Success: 42004 | Failed: 42005]
 */
- (NSArray *)eccSha1SignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias;

/**
 * This API hashes the input data using SHA-256 cryptographic hash function and signs the digest
 * using the ECC key associated with the specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   keyAlias    The alias of the ECC key.
 * @return              [Success: 42004 | Failed: 42005]
 */
- (NSArray *)eccSha256SignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias;

/**
 * This API verifies the signature of the input data using the ECC key associated with the
 * specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   signature   The byte array that contains the signature.
 * @param   keyAlias    The alias of the ECC key.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42006 | Failed: 42007]
 */
- (int)eccVerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias;

/**
 * This API hashes the input data using SHA-1 cryptographic hash function and verifies the
 * signature of the digest using the ECC key associated with the specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   signature   The byte array that contains the signature.
 * @param   keyAlias    The alias of the ECC key.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42006 | Failed: 42007]
 */
- (int)eccSha1VerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias;

/**
 * This API hashes the input data using SHA-256 cryptographic hash function and verifies the
 * signature of the digest using the ECC key associated with the specified key alias.
 * @param   inputData   The byte array that contains the input data.
 * @param   signature   The byte array that contains the signature.
 * @param   keyAlias    The alias of the ECC key.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42006 | Failed: 42007]
 */
- (int)eccSha256VerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias;

/**
 * This API adds a certificate chain to the list of trusted certificates in the keystore.
 * @param   certArray   The array that contains the chain of three certificates in the following
 *                      order: user certificate, intermediate certificate, and root certificate.
 * @param   certAlias   The alias of the certificate.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42008 | Failed: 42009]
 */
- (int)addCertWithAlias:(NSArray *)certArray andCertAlias:(int)certAlias;

/**
 * This API gets the certificate chain associated with the specified certificate alias from the
 * keystore.
 * @param   certAlias   The alias of the certificate.
 * @return              This API returns the array of certificate chain associated with the
 *                      specified certificate alias in the keystore.
 * @return              [Success: 42010 | Failed: 42011]
 */
- (NSArray *)getCertWithAlias:(int)certAlias;

/**
 * This API deletes a certificate chain from the list of trusted certificates from the keystore.
 * @param   certAlias   The alias of the certificate.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42012 | Failed: 42013]
 */
- (int)deleteCertWithAlias:(int)certAlias;

/**
 * This API checks the existence of the ECC key in the keystore by using its key alias.
 * @param   keyAlias    The alias of the ECC key.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42022 | Failed: 42018, 42023]
 */
- (int)isKeyAlias:(int)keyAlias;

/**
 * This API checks the existence of the certificate in the keystore by using its certificate alias.
 * @param   certAlias   The alias of the certificate.
 * @return              This API returns one of the result code integers.
 * @return              [Success: 42022 | Failed: 42019, 42023]
 */
- (int)isCertAlias:(int)certAlias;

/**
 * This API gets all the ECC key aliases from the keystore.
 * @return  This API returns the array of ECC key aliases in the keystore.
 * @return  [Success: 42014 | Failed: 42015]
 */
- (NSArray *)getKeyAliases;

/**
 * This API gets all the certificate aliases from the keystore.
 * @return  This API returns the array of certificate aliases in the keystore.
 * @return  [Success: 42016 | Failed: 42017]
 */
- (NSArray *)getCertAliases;

/**
 * This API deletes all the ECC keys, certificates, and resets the PIN.
 * @return  This API returns one of the result code integers.
 * @return  [Success: 42020 | Failed: 42021]
 */
- (int)resetTokenFirmware;
/*========================= Generic PKI API Ends =========================*/


/*========================= Utility API for Zipping and Unzipping Starts =========================*/

/**
 * This API helps to create zip file with the use of SSZipArchive library.
 */
- (BOOL)createZipFile:(nonnull NSString *)path withFilesAtPaths:(nonnull NSArray *)paths;

/**
 * This API helps to unzip file to destination with the use of SSZipArchive library.
 */
- (BOOL)unzipFileAtPath:(nonnull NSString *)path toDestination:(nonnull NSString *)destionation;

/*========================= Utility API for Zipping and Unzipping Ends =========================*/

@end
