<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/DistinguishedName.h</key>
		<data>
		nlvgrmnviVadBrI5HclYTNu00Zk=
		</data>
		<key>Headers/ResultCode.h</key>
		<data>
		ZcfP28knt3SU2tVrdeymQdfQeo4=
		</data>
		<key>Headers/VTap.h</key>
		<data>
		pSivbNdSklbQUP7+NDKzFDFMECg=
		</data>
		<key>Headers/VTapManager.h</key>
		<data>
		lwZJgVXjwrpZmEtMLCX9Hs36VFI=
		</data>
		<key>Info.plist</key>
		<data>
		wtSf4KkVf62Y6ka/5iLRMLCHps0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		VqjlbpMZfLElRLm1f0sbfgkrYrU=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
		</data>
		<key>VTap_info.plist</key>
		<data>
		GL9F3z/uI5mele4QfXaSvmZVJS4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/DistinguishedName.h</key>
		<dict>
			<key>hash</key>
			<data>
			nlvgrmnviVadBrI5HclYTNu00Zk=
			</data>
			<key>hash2</key>
			<data>
			pk5uBcbLvDXsdilrIrELqoLNGHjB5bF4HeEIKTf89+s=
			</data>
		</dict>
		<key>Headers/ResultCode.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZcfP28knt3SU2tVrdeymQdfQeo4=
			</data>
			<key>hash2</key>
			<data>
			pn/v16gkbG4hwkcSvbmp3Q2gtqWmrEA6AbZZRlnzxb4=
			</data>
		</dict>
		<key>Headers/VTap.h</key>
		<dict>
			<key>hash</key>
			<data>
			pSivbNdSklbQUP7+NDKzFDFMECg=
			</data>
			<key>hash2</key>
			<data>
			8wOI8oDsXb2vGIpsiNjrOoyyXhntJZFAWoMlBgqgXC0=
			</data>
		</dict>
		<key>Headers/VTapManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lwZJgVXjwrpZmEtMLCX9Hs36VFI=
			</data>
			<key>hash2</key>
			<data>
			/pfkRkqfOAY4/voTKpZbWbjHNRaVTcpRqxkmqYkn+0c=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			VqjlbpMZfLElRLm1f0sbfgkrYrU=
			</data>
			<key>hash2</key>
			<data>
			sdBEc3zjmASrHXjGYU/5m4GV5jgGKdyZrAKMPgZwstA=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
			</data>
			<key>hash2</key>
			<data>
			kvW/0Vum+eOfqkjK981O2CnkzdO7H09ANCWrb1jg/O0=
			</data>
		</dict>
		<key>VTap_info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			GL9F3z/uI5mele4QfXaSvmZVJS4=
			</data>
			<key>hash2</key>
			<data>
			742Y5KEiu8wHnuXe1usjx9t8qgqaRNtPQJcuolqPATk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
