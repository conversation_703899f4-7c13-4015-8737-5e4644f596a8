//
//  ResultCode.h
//  VTap
//
//  Created by V-Key on 6/3/15.
//  Copyright (c) 2015 vkey. All rights reserved.
//

// TA TAG
// enum TA_TYPE {typeA, typeB};
//
// enum TA_TYPE categoryForIndex(int index) {
//    if (index > 1 && index < 4) {
//        return typeA;
//    } else {
//        return typeB;
//    }
//}

typedef NS_ENUM(NSInteger, TokenType) {
    PKI_TOKEN = 3,      /*!< PKI token type. This can be either a normal PKI token or a FIDO2 token. */
    OTP_TOKEN = 2,      /*!< OTP token type. */
    UNKNOWN_TOKEN = 1   /*!< Unknown token type. */
};

typedef NS_ENUM(NSInteger, PNSType) {
    APNS = 0,    /*!<Apple Push Notification service. */
    FCM = 1,     /*!< Fire base Cloud Messenging. */
    HMS = 2      /*!< Huawei Mobile Services. */
};

/*----------- Setup VTap -----------*/

/**
 * Successfully set up V-OS Smart Token. This operation includes:
 * - Started V-OS
 * - Started V-OS App Protection
 * - Set trusted time server (V-OS Smart Token Server)
 * - Loaded and initialized firmware if provisioning has already been done
 * @see VTapManager.setupVTap:()
 */
#define VTAP_SETUP_SUCCESS 40200

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The V-OS App Protection setup operation failed due to one or more of the following reasons:
 * - Missing/corrupted profile and/or signature
 * - Profile and/or signature decryption error
 * - Invalid signature
 * - Missing native library (for SDK version <3.6.1.6)
 * - Unsatisfied link error in `.so` files (for SDK version <3.6.1.6)
 * - Emulator detected
 * __Solution(s):__
 * - Make sure that the profile, firmware, and signature files are correct and in the `/assets/`
 * folder of your app project
 * - Make sure that the profile is downloaded from the correct customer ID in V-OS App
 * Protection Server
 * - If emulator is running, set `VGuardFactory.debug = true`
 * @see VTapManager.setupVTap:()
 */
#define VGUARD_FAILED 40202

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * V-OS failed to start due to one or more of the following reasons:
 * - Missing/corrupted firmware and/or license
 * - Signer certificate in license mismatch
 * - Package name in license mismatch
 * - Error in license verification
 * __Solution(s):__
 * - Make sure that the firmware and license files are correct and in the `/assets/` folder of
 * your app project
 * - Make sure that the app is signed by the correct private key whose public key is used to
 * generate the license
 * - Make sure that the app package name/bundle ID matches with the one given for license
 * generation
 * @see VTapManager.setupVTap:()
 */
#define VOS_FAILED 40203

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Smart tokens cannot be loaded due to one or more of the following reasons:
 * - Failed to load V-OS Smart Token TA because `vtapta.bin` file is missing/corrupted in the
 * app files directory
 * - TA secure store error
 * __Solution(s):__
 * - Make sure that the `vtap.bin` file is available in the app files directory
 * - Make sure that the DFP# is not changed in the device
 * @see VTapManager.setupVTap:()
 */
#define VTAP_TOKEN_LOAD_FAILED 40204

/**
 * __Cause(s):__
 * The V-OS Smart Token setup operation failed due to one or more of the following reasons:
 * - Failed to set trusted time server URL because V-OS is locked due to threats detected by
 * V-OS App Protection
 * __Solution(s):__
 * - Make sure that there is no threat on the device
 * - Make sure that the device is not locked by verifying the troubleshooting ID of the device
 * in V-OS App Protection Server. If so, unlock the device.
 * @see VTapManager.setupVTap:()
 */
#define VTAP_SETUP_FAILED 40205

/**
 * __Cause(s):__
 * V-OS failed to read trusted storage.
 * __Solution(s):__
 * Call resetVOSTrustedStorage.
 */
#define VOS_FAILED_WITH_TRUST_STORAGE_ERROR 40207

/**
 * __Cause(s):__
 * V-OS failed while running execute API to check for existing instance.
 */
#define VOS_FAILED_AT_EXECUTE_API 40209

/**
 * __Cause(s):__
 * V-OS existed, but host app is claiming that it doesn't.
 */
#define VTAP_SETUP_FAILED_AND_MISUSED 40210

/**
 * __Cause(s):__
 * V-OS failed, unable to retrieve keychain attribute.
 */
#define VOS_FAILED_WITH_KEYCHAIN_ERROR 40211

/*----------- Device Compatibility Check -----------*/

/**
 * The current device model is a whitelisted device model. You may allow the user to continue
 * using the app.
 * @see VTapManager.checkDeviceCompatibility:()
 */
#define VTAP_WHITE_LISTED_DEVICE 40300

/**
 * The current device model is blacklisted. You can verify this by comparing the
 * device model with the list of blacklisted device models in the V-OS Smart Token database.
 * If you receive this error, you should not allow the user to continue using the app on the
 * device. If you wish to allow the user to continue using the app, unexpected behavior on V-OS
 * Smart Token may occur. You should inform the user that the device model is not supported.
 * @see VTapManager.checkDeviceCompatibility:()
 */
#define VTAP_BLACK_LISTED_DEVICE 40301

/**
 * The current device model is neither a blacklisted nor whitelisted. You need to
 * call the `sendDeviceInfo` API so that the device information can be captured and analyzed.
 * You may allow the user to continue using the device provided you have informed the user about
 * the risk.
 * @see VTapManager.checkDeviceCompatibility:()
 */
#define VTAP_GREY_LISTED_DEVICE 40302

/**
 * __Cause(s):__
 * The get device list web service call failed due to one or more of the following reasons:
 * - V-OS Smart Token Server is down or not responding (HTTP 500 or any other HTTP response
 * code other than HTTP 200)
 * - Client authentication fail (not able to get customer ID when V-OS is not running)
 * __Solution(s):__
 * - Make sure that the V-OS Smart Token Server is running and responsive
 * - Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.checkDeviceCompatibility:()
 */
#define VTAP_GET_DEVICE_LIST_FAILED 40303

/*----------- Send Device Info -----------*/

/**
 * The device information (platform, model, OS) along with customer ID, DFP#, greylist, and
 * status has been sent successfully.
 * @see VTapManager.sendDeviceInfostatus:()
 */
#define VTAP_SEND_DEVICE_INFO_SUCCESS 40400

/**
 * __Cause(s):__
 * The Send Device Information web service call failed due to one or more of the following reasons:
 * - V-OS Smart Token Server is down or not responding (HTTP 500 or any other HTTP response
 * code other than HTTP 200)
 * - Client authentication fail (not able to get customer ID when V-OS is not running)
 * __Solution(s):__
 * - Make sure that the V-OS Smart Token Server is running and responsive
 * - Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.sendDeviceInfostatus:()
 */
#define VTAP_SEND_DEVICE_INFO_FAILED 40401

/*----------- Send Troubleshooting Logs -----------*/

/**
 * The troubleshooting log (with DFP# and JSON data generated) has been sent to the server
 * successfully and received the HTTP 200 response.
 * @see VTapManager.sendTroubleshootingLogs:()
 */
#define VTAP_SEND_TROUBLESHOOTING_LOGS_SUCCESS 40502

/**
 * __Cause(s):__
 * The send troubleshooting log web service call failed due to one or more of the following reasons:
 * - Problem occurred during DFP# generation or DFP# is missing
 * - Problem occurred during JSON data generation
 * - POST request error in server
 * - Did not receive HTTP 200 response from server
 * __Solution(s):__
 * - Check DFP#
 * - Check server connection
 * @see VTapManager.sendTroubleshootingLogs:()
 */
#define VTAP_SEND_TROUBLESHOOTING_LOGS_FAILED 40503

/*----------- Provisioning -----------*/

/**
 * The token firmware is downloaded and loaded successfully. This operation includes:
 * - Downloaded token firmware from V-OS Provisioning Server
 * - Loaded the downloaded firmware file using `loadToken:()Firmware` API
 * - Sent acknowledgement to V-OS Provisioning Server
 * @see VTapManager.getLoadAckTokenFirmware:()
 */
#define VTAP_TOKEN_DOWNLOAD_SUCCESS 40600

/**
 * __Cause(s):__
 * The provisioning info obtained is invalid due to one or more of the following reasons:
 * - The provisioning info array size is not two (token serial and APIN)
 * - The provisioning info array is `null` or empty
 * __Solution(s):__
 * Make sure that the input parameters are correct.
 * @see VTapManager.getLoadAckTokenFirmware:()
 */
#define VTAP_ERROR_INVALID_PROVISIONING_INFO 40601

/**
 * __Cause(s):__
 * The token serial in the provisioning info obtained is invalid due to one or more of the
 * following reasons:
 * - The token serial is `null` or empty
 * - The token serial length is not equal to 10
 * __Solution(s):__
 * Make sure that the input parameters are correct.
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_ERROR_INVALID_TOKEN_SERIAL 40602

/**
 * __Cause(s):__
 * The activation PIN (APIN) in the provisioning info obtained is invalid due to one or more of
 * the following reasons:
 * - The APIN is `null` or empty
 * - The APIN length is not equal to 16
 * __Solution(s):__
 * Make sure that the input parameters are correct.
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_ERROR_INVALID_ACTIVATION_PIN 40603

/**
 * __Cause(s):__
 * The token firmware download operation failed due to one or more of the following reasons:
 * - V-OS Provisioning Server is down (HTTP 500 response or no response)
 * - Client authentication failed (not able to get customer ID when V-OS is not running)
 * __Solution(s):__
 * - Check server connection
 * - Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_TOKEN_DOWNLOAD_FAILED 40604

/**
 * __Cause(s):__
 * The token firmware download operation failed due to one or more of the following reasons:
 * - Received HTTP 204 from V-OS Provisioning Server
 * - The firmware requested is not found or already provisioned
 * - Invalid token serial
 * __Solution(s):__
 * - Make sure that the token exists in database of V-OS Provisioning Server
 * - Make sure that the token is not already provisioned
 * - Make sure that the token serial is correct
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_TOKEN_NOT_FOUND 40605

/**
 * __Cause(s):__
 * The token firmware download operation failed due to one or more of the following reasons:
 * - Received HTTP 400 from V-OS Provisioning Server
 * - Bad request
 * - Invalid DFP#
 * - Invalid customer ID
 * __Solution(s):__
 * - Make sure that the `setupVTap:()` API call was successful and returned valid DFP# and customer
 * ID
 * - Check the database of V-OS Provisioning Server and logs for the availability of the customer
 * ID and firmware requested
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_TOKEN_BAD_REQUEST 40606

/**
 * __Cause(s):__
 * V-OS returns null DFP because V-OS is locked due to various security reasons.
 * __Solution(s):__
 * - Host app should inform user that the provisioning operation cannot continue due to security
 * issues identified in the device.
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.getLoadAckTokenFirmware:httpPostMethod:()
 */
#define VTAP_TOKEN_INVALID_DFP 40607

/**
 * The token firmware is loaded successfully. This operation includes:
 * - Unzipped the downloaded firmware
 * - Loaded and initialized TA
 * - Processed manifest using APIN and DFP#
 * - Cleanup: Deleted the manifest file and downloaded ZIP file
 * - Stored preference data to indicate provisioning is done successfully
 * - Sent device info if greylisted device indicating provisioning status
 * @see VTapManager.loadTokenFirmware:APIN:downloadFilePath:()
 */
#define VTAP_LOAD_FIRMWARE_SUCCESS 40608

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The token firmware ZIP file cannot be unzipped due to one or more of the following reasons:
 * - The ZIP file is corrupted
 * - The ZIP file does not exist in the app files directory
 * __Solution(s):__
 * - Check the ZIP file passed as parameter
 * - Download and use another token
 * @see VTapManager.loadTokenFirmware:APIN:downloadFilePath:()
 */
#define VTAP_TOKEN_UNZIP_FAILED 40609

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The manifest file processing operation failed due to one or more of the following reasons:
 * - Invalid APIN
 * - Invalid DFP#
 * - Manifest file missing in the extracted ZIP file path
 * __Solution(s):__
 * - Check APIN
 * - Check manifest file
 * - Download and use another token
 * @see VTapManager.loadTokenFirmware:APIN:downloadFilePath:()
 */
#define VTAP_TOKEN_PROCESSING_FAILED 40610

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Token firmware cannot be loaded due to one or more of the following reasons:
 * - V-OS is not running
 * - V-OS is locked
 * __Solution(s):__
 * Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.loadTokenFirmware:APIN:downloadFilePath:()
 */
#define VTAP_LOAD_FIRMWARE_FAILED 40611

/**
 * Successfully loaded the provisoned token on the device.
 * @see VTapManager.loadToken:()
 */
#define VTAP_LOAD_TOKEN_SUCCESS 40612

/**
 * Failed to load the provisoned token on the device.
 * @see VTapManager.loadToken:()
 */
#define VTAP_LOAD_TOKEN_FAILED 40613

/**
 * Successfully unloaded the token from the device.
 * @see VTapManager.unloadToken:()
 */
#define VTAP_UNLOAD_TOKEN_SUCCESS 40614

/**
 * Failed to unload the token from the device.
 * @see VTapManager.unloadToken:()
 */
#define VTAP_UNLOAD_TOKEN_FAILED 40615

#define VTAP_SET_DEFAULT_TOKEN_SUCCESS 40616

#define VTAP_TOKEN_NOT_PROVISIONED 40617

/**
 * Successfully removed the token from the device.
 * @see VTapManager.removeTokenFirmware:()
 */
#define VTAP_REMOVE_TOKEN_SUCCESS 40618

/**
 * Failed to remove the token from the device.
 * @see VTapManager.removeTokenFirmware:()
 */
#define VTAP_REMOVE_TOKEN_FAILED 40619

/*----------- User PIN Registration -----------*/

/**
 * Token PIN is created in TA secure store successfully.
 * @see VTapManager.createTokenPIN:withTokenSerial:()
 */
#define VTAP_CREATE_PIN_SUCCESS 40700

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Failed to create token PIN due to one or more of the following reasons:
 * - V-OS is not running
 * - The length of PIN is incorrect
 * __Solution(s):__
 * - Make sure that the `setupVTap:()` API call was successful
 * - Make sure that the length of PIN is correct
 * @see VTapManager.createTokenPIN:withTokenSerial:()
 */
#define VTAP_CREATE_PIN_FAILED 40701

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Failed to create token PIN because the token PIN has already been created before.
 * __Solution(s):__
 * - If the old PIN is known, use the `changePIN` API to set a new PIN
 * - If the old PIN is unknown, use the `removeTokenFirmware:()` to delete the provisioned token
 * and do provisioning again
 * @see VTapManager.createTokenPIN:withTokenSerial:()
 */
#define VTAP_PIN_ALREADY_EXIST 40702

/**
 * The token PIN validation is successfully.
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:andSeed:()
 */
#define VTAP_CHECK_PIN_SUCCESS 40800

/**
 * __Cause(s):__
 * The token PIN validation failed due to one of more of the following reasons:
 * - The token PIN input is incorrect
 * - V-OS is not running
 * - V-OS is locked
 * __Solution(s):__
 * - Make sure that the `setupVTap:()` API call was successful
 * - Host app should inform the user of incorrect PIN and prompt for PIN again
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:andSeed:()
 */
#define VTAP_CHECK_PIN_FAILED 40801

/**
 * __Cause(s):__
 * V-OS is locked due to security reason.
 * __Solution(s):__
 * - Check if the device is locked through V-OS App Protection Server with the troubleshooting ID.
 * If the device is locked, unlock the device.
 * - If the `checkTokenPin` API is called with too many wrong PIN attempts, a delay is induced by
 * TA and the device is finally locked. Restart from `setupVTap:()` and use the correct PIN.
 * @see VTapManager.createTokenPIN:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:andSeed:()
 * @see VTapManager.changeTokenPIN:withNewPIN:withTokenSerial:()
 * @see VTapManager.generateTOTP:()
 * @see VTapManager.generateCR:()
 * @see VTapManager.generateTxS:amount:()
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 * @see VTapManager.pkiFunctionCheckPin:withPin:andRememberPin:()
 */
#define VTAP_VOS_LOCKED 40802

/**
 * The token PIN has been changed successfully.
 * @see VTapManager.changeTokenPIN:withNewPIN:withTokenSerial:()
 */
#define VTAP_CHANGE_PIN_SUCCESS 40900

/**
 * __Cause(s):__
 * Unable to change the token PIN due to one or more of the following reasons:
 * - Incorrect PIN length
 * - V-OS Smart Token is not set up correctly
 * __Solution(s):__
 * - Make sure that the PIN length is correct
 * - Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.changeTokenPIN:withNewPIN:withTokenSerial:()
 */
#define VTAP_CHANGE_PIN_FAILED 40901

/**
 * __Cause(s):__
 * Incorrect existing PIN is given during change PIN operation.
 * __Solution(s):__
 * Host app should inform user of incorrect existing PIN and prompt for PIN again.
 * @see VTapManager.changeTokenPIN:withNewPIN:withTokenSerial:()
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 */
#define VTAP_CHANGE_INCORRECT_PIN_FAILED 40902

/*----------- Transaction -----------*/

/**
 * Successfully set the OTP length.
 * @see VTapManager.setOtpLength:()
 */
#define VTAP_SET_OTP_LENGTH_SUCCESS 41000

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to set the OTP length due to incorrect OTP length given.
 * __Solution(s):__
 * OTP length should be set to `6`, `7`, or `8`.
 * @see VTapManager.setOtpLength:()
 */
#define VTAP_SET_OTP_LENGTH_FAILED 41001

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate OTP due to one or more of the following reasons:
 * - TA is not loaded or initialized
 * - V-OS is not running
 * __Solution(s):__
 * Make sure that the `setupVTap()` API call was successful.
 * @see VTapManager.generateTOTP:()
 */
#define VTAP_GENERATE_TOTP_FAILED 41002

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The key option given is not supported.
 * __Solution(s):__
 * Make sure that the input key option parameter is `1` or `2`.
 * @see VTapManager.generateTOTP:()
 */
#define VTAP_TOTP_UNSUPPORTED_KEY_OPTION 41003

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate challenge response (CR) due to one or more of the following reasons:
 * - TA is not loaded or initialized
 * - V-OS is not running
 * __Solution(s):__
 * Make sure that the `setupVTap()` API call was successful.
 * @see VTapManager.generateCR:()
 */
#define VTAP_GENERATE_CR_FAILED 41004

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The challenge message length given has one of the following problem:
 * - Is null, empty, or 0
 * - Length is greater than 40
 * __Solution(s):__
 * Make sure that the length of the input `crMsg` is shorter than or equal to 40 and has actual
 * content.
 * @see VTapManager.generateCR:()
 */
#define VTAP_CR_UNSUPPORTED_MSG_LENGTH 41005

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate transaction signing due to one or more of the following reasons:
 * - TA is not loaded or initialized
 * - V-OS is not running
 * __Solution(s):__
 * Make sure that the `setupVTap:()` API call was successful.
 * @see VTapManager.generateTxS:amount:()
 */
#define VTAP_GENERATE_TXS_FAILED 41006

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate OCRA due to the input parameters (`account` and `amount`) has one of the
 * following problem:
 * - Is null, empty, 0, or invalid number
 * - Combined length is greater than 40
 * __Solution(s):__
 * Make sure that the combined length of the input parameters is shorter than or equal to 40
 * and has actual content.
 * @see VTapManager.generateTxS:amount:()
 */
#define VTAP_TXS_UNSUPPORTED_MSG_LENGTH 41007

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate OTP due to unsupported OTP length detected.
 * __Solution(s):__
 * OTP length should be set to `6`, `7`, or `8`.
 * @see VTapManager.generateTOTP:()
 * @see VTapManager.generateCR:()
 * @see VTapManager.generateTxS:amount:()
 */
#define VTAP_UNSUPPORTED_OTP_LENGTH 41008

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Unable to generate OTP due to one or more of the following reasons:
 * - `checkPIN` API not called
 * - `createPIN` API not called
 * - `rememberPIN` not set
 * __Solution(s):__
 * Make sure that token PIN is correctly set and validated.
 * @see VTapManager.generateTOTP:()
 * @see VTapManager.generateCR:()
 * @see VTapManager.generateTxS:amount:()
 */
#define VTAP_INCORRECT_API_CALL_SEQUENCE 41009

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * Secure time is not available or invalid due to one or more of the following reasons:
 * - Device is rebooted
 * - Device time is changed either manually or automatically
 * - Device time is changed due to unknown reason
 * __Solution(s):__
 * - Call the `getTrustedTime:()` API to obtain the secure time
 * - If setup is required, call the `setupVTap:()` API which also obtains the secure time from the
 * V-OS Smart Token Server
 * @see VTapManager.generateTOTP:()
 * @see VTapManager.generateCR:()
 * @see VTapManager.generateTxS:amount:()
 */
#define VTAP_TIME_FAILURE 41010

/*----------- Other Results -----------*/

/**
 * > Note: This error should only happen during development. This error is applicable to all
 * APIs that requires input parameter(s).
 * __Cause(s):__
 * One or more of the input parameters are invalid.
 * __Solution(s):__
 * Make sure that the input parameter(s) is/are valid.
 * @see VTapManager.vMessageDownload:messageType:andMessageFlag:()
 * @see VTapManager.vMessageDecrypt:()
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 * @see VTapManager.pushNotificationRegister:deviceId:andToken:()
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 * @see VTapManager.pkiFunctionCheckPin:withPin:andRememberPin:()
 * @see VTapManager.generateCsrAndSend:distinguishedName:pin:andRememberPin:()
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 * @see VTapManager.changeTokenPIN:withNewPIN:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:()
 * @see VTapManager.checkTokenPIN:remember:withTokenSerial:andSeed:()
 * @see VTapManager.createTokenPIN:withTokenSerial:()
 */
#define VTAP_INVALID_INPUT 41011

/**
 * > Note: This error is applicable to all APIs that require connect to server through internet.
 * __Cause(s):__
 * Unable to connect to the server.
 * __Solution(s):__
 * Make sure that the SDK is able to connect to the V-OS Smart Token Server and V-OS
 * Provisioning Server.
 * @see VTapManager.sendTroubleshootingLogs:()
 * @see VTapManager.vMessageAck:()
 * @see VTapManager.vMessageDownload:messageType:andMessageFlag:()
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 * @see VTapManager.generateCsrAndSend:distinguishedName:pin:andRememberPin:()
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 * @see VTapManager.getLoadAckTokenFirmware:()
 * @see VTapManager.sendDeviceInfostatus:()
 * @see VTapManager.checkDeviceCompatibility:()
 */
#define VTAP_ERROR_CONNECTION_FAILED 41012

/**
 * > Note: This error should only happen during development.
 * __Cause(s):__
 * The API is called in the wrong sequence, e.g. generate TOTP before provisioning is done.
 * __Solution(s):__
 * Make sure that the required APIs are called before calling the current API.
 * @see VTapManager.vMessageDecrypt:()
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 * @see VTapManager.pkiFunctionCheckPin:withPin:andRememberPin:()
 * @see VTapManager.generateCsrAndSend:distinguishedName:pin:andRememberPin:()
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 */
#define VTAP_INVALID_API_SEQUENCE 41013

/*----------- PKI Specific -----------*/

/**
 * The push notification registration is successful.
 * @see VTapManager.pushNotificationRegister:deviceId:andToken:()
 */
#define VTAP_PUSH_NOTIFICATION_REGISTRATION_SUCCESS 41014

/**
 * The push notification registration is not successful
 * @see VTapManager.pushNotificationRegister:deviceId:andToken:()
 */
#define VTAP_PUSH_NOTIFICATION_REGISTRATION_FAILED 41015

/*----------- Trigger Push Notification -----------*/

/**
 * Successfully triggered the push notification to get the status of CSR from the V-OS PKI
 * Token Server.
 * @see VTapManager.triggerCertPushNotification:()
 */
#define VTAP_PKI_TRIGGER_SEND_CERT_SUCCESS 41140

/**
 * __Cause(s):__
 * Unable to trigger the push notification to get the status of CSR from the V-OS PKI Token Server.
 * @see VTapManager.triggerCertPushNotification:()
 */
#define VTAP_PKI_TRIGGER_SEND_CERT_FAILED 41141

/**
 * Certificate activation is sent successfully.
 */
#define VTAP_PKI_SEND_CERT_ACTIVATION_SUCCESS 41130

/**
 * Unable to send certificate activation.
 */
#define VTAP_PKI_SEND_CERT_ACTIVATION_FAILED 41131

/**
 * The CSR registration is successful.
 * @see VTapManager.generateCsrAndSend:distinguishedName:pin:andRememberPin:()
 */
#define VTAP_PKI_CSR_REGISTRATION_SUCCESS 41100

/**
 * __Cause(s):__
 * The CSR registration is not successful due to one or more of the following reasons:
 * - V-OS is unable to generate CSR with the provided details
 * - Fail to create JSON object
 * - Server returns error or status code other than HTTP200
 * @see VTapManager.generateCsrAndSend:distinguishedName:pin:andRememberPin:()
 */
#define VTAP_PKI_CSR_REGISTRATION_FAILED 41101

/**
 * The authentication is successful.
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 */
#define VTAP_PKI_AUTHENTICATION_SUCCESS 41102

/**
 * __Cause(s):__
 * The authentication is not successful due to one or more of the following reasons:
 * - The app is not able to do authentication signing
 * - The signature verification failed at the server
 * - Fail to create JSON object
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 */
#define VTAP_PKI_AUTHENTICATION_FAILED 41103

/**
 * __Cause(s):__
 * The host app tries to do authentication signing but the certificate for AS is not present on
 * the device.
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 */
#define VTAP_PKI_AUTHENTICATION_CERT_NOT_AVAILABLE 41105

/**
 * @exclude from doc
 */
#define VTAP_PKI_DOC_SIGNING_SUCCESS 41106

/**
 * @exclude from doc
 */
#define VTAP_PKI_DOC_SIGNING_FAILED 41107

/**
 * @exclude from doc
 */
#define VTAP_PKI_DOC_SIGNING_CERT_NOT_AVAILABLE 41109

/**
 * Failed to download the document or 2-pass message from the server.
 * @see VTapManager.vMessageDownload:messageType:andMessageFlag:()
 */
#define VTAP_PKI_MESSAGE_DOWNLOAD_FAILED 41111

/**
 * The host app is able to download the certificate successfully.
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 */
#define VTAP_PKI_CERT_DOWNLOAD_SUCCESS 41112

/**
 * The host app is unable to download the certificate due to error.
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 */
#define VTAP_PKI_CERT_DOWNLOAD_FAILED 41113

/**
 * The PIN validation is successfully.
 * @see VTapManager.pkiFunctionCheckPin:withPin:andRememberPin:()
 */
#define VTAP_PKI_CHECK_PIN_SUCCESS 41114

/**
 * __Cause(s):__
 * The PIN validation failed due to one of more of the following reasons:
 * - The PIN input is incorrect
 * - V-OS is not running
 * - V-OS is locked
 * __Solution(s):__
 * - Make sure that the `setupVTap:()` API call was successful
 * - Host app should inform the user of incorrect PIN and prompt for PIN again
 * @see VTapManager.pkiFunctionCheckPin:withPin:andRememberPin:()
 */
#define VTAP_PKI_CHECK_PIN_FAILED 41115

/**
 * The PIN has been changed successfully.
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 */
#define VTAP_PKI_CHANGE_PIN_SUCCESS 41116

/**
 * __Cause(s):__
 * Unable to change the PIN due to one or more of the following reasons:
 * - V-OS Smart Token is not set up correctly
 * - V-OS is not running
 * - V-OS is locked
 * __Solution(s):__
 * - Make sure that the `setupVTap:()` API call was successful
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 */
#define VTAP_PKI_CHANGE_PIN_FAILED 41117

/**
 * __Cause(s):__
 * Incorrect existing PIN is given during change PIN operation.
 * __Solution(s):__
 * Host app should inform user of incorrect existing PIN and prompt for PIN again.
 * @see VTapManager.pkiFunctionChangePIN:oldPin:withNewPIN:()
 */
#define VTAP_PKI_CHANGE_INCORRECT_PIN_FAILED 41119

/**
 * __Cause(s):__
 * The host app tries to decrypt the message when V-OS Messaging certificate is not present on
 * the device.
 * @see VTapManager.vMessageDecrypt:()
 */
#define VTAP_PKI_VMESSAGE_CERT_NOT_AVAILABLE 41121

/**
 * __Cause(s):__
 * The certificate verification failed due to one or more of the following reasons:
 * - Certificate chain verification failed
 * - Signature verification failed
 * - V-OS is locked
 * @see VTapManager.pkiCertDownload:messageId:andMessageType:()
 */
#define VTAP_PKI_CERT_VERIFICATION_FAILED 41123

/**
 * __Cause(s):__
 * Failed to decrypt the message due to one or more of the following reasons:
 * - Decryption of message failed
 * - V-OS is unable to decrypt
 * - V-OS is locked
 * @see VTapManager.vMessageDecrypt:()
 */
#define VTAP_VMESSAGE_DECRYPT_MSG_FAILED 41125

/**
 * Acknowledgment is sent to the server successfully.
 * @see VTapManager.vMessageAck:()
 */
#define VTAP_VMESSAGE_SEND_ACK_SUCCESS 41126

/**
 * __Cause(s):__
 * Failed to send acknowledgement to the server due to one or more of the following reasons:
 * - Acknowledgment to server failed
 * - Error or HTTP status code not equal to 200
 * - JSON object creation failed
 * @see VTapManager.vMessageAck:()
 */
#define VTAP_VMESSAGE_SEND_ACK_FAILED 41127

/**
 * __Cause(s):__
 * Failed to download the certificate because the cert has already downloaded before:
 * - Cert download failed
 * - HTTP status code will be 200, but the cert data will return null by server
 * @see VTapManager.pkiCertDownload:()
 */
#define VTAP_PKI_CERT_ALREADY_DOWNLOADED_FAILED 41129

/**
 * The authentication request is rejected by the host app successfully.
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 */
#define VTAP_PKI_AUTHENTICATION_REJECTION_SUCCESS 41160

/**
 * __Cause(s):__
 * Unable to reject the authentication request.
 * @see VTapManager.pkiFunctionAuthenticate:dataToBeSigned:andReject:()
 */
#define VTAP_PKI_AUTHENTICATION_REJECTION_FAILED 41161

#define VTAP_CREATE_ADDITIONAL_DATA_SUCCESS 40903

/**
 *
 * @see VTapManager.setMessageTypeData:()
 * @see VTapManager.disableAdditionalData:()
 */
#define VTAP_INVALID_FORMAT 41018

/**
 * Successfully clears the additional data set in a particular data type.
 * @see VTapManager.disableAdditionalData:()
 */
#define VTAP_DISABLE_ADDITIONAL_DATA 41019

/**
 * Successfully set the message notification type required by the additional data mechanism.
 * @see VTapManager.setMessageTypeData:()
 */
#define VTAP_CREATE_ADDITIONAL_MESSAGE_TYPE_SUCCESS 41020

/*----------- Generic PKI APIs -----------*/

/**
 * Successfully generated ECC CSR with the ECC key associated with the particular key alias.
 */
#define VTAP_GENERATE_ECC_CSR_WITH_ALIAS_SUCCEEDED 42000

/**
 * Failed to generate ECC CSR with the ECC key associated with the particular key alias.
 */
#define VTAP_GENERATE_ECC_CSR_WITH_ALIAS_FAILED 42001

/**
 * Successfully deleted the ECC key from the keystore.
 */
#define VTAP_DELETE_ECC_KEY_WITH_ALIAS_SUCCEEDED 42002

/**
 * Failed to delete the ECC key from the keystore.
 */
#define VTAP_DELETE_ECC_KEY_WITH_ALIAS_FAILED 42003

/**
 * Successfully signed the input data with the ECC key associated with the particular key alias.
 */
#define VTAP_ECC_SIGN_WITH_ALIAS_SUCCEEDED 42004

/**
 * Failed to sign the input data with the ECC key associated with the particular key alias.
 */
#define VTAP_ECC_SIGN_WITH_ALIAS_FAILED 42005

/**
 * Successfully verified the signature with the ECC key associated with the particular key alias.
 */
#define VTAP_ECC_VERIFY_WITH_ALIAS_SUCCEEDED 42006

/**
 * Failed to verify the signature with the ECC key associated with the particular key alias.
 */
#define VTAP_ECC_VERIFY_WITH_ALIAS_FAILED 42007

/**
 * Successfully added the certificate chain to the list of trusted certificates in the keystore.
 */
#define VTAP_ADD_CERT_WITH_ALIAS_SUCCEEDED 42008

/**
 * Failed to add the certificate chain to the list of trusted certificates in the keystore.
 */
#define VTAP_ADD_CERT_WITH_ALIAS_FAILED 42009

/**
 * Successfully obtained the certificate associated with the particular certificate alias.
 */
#define VTAP_GET_CERT_WITH_ALIAS_SUCCEEDED 42010

/**
 * Failed to obtain the certificate associated with the certificate key alias.
 */
#define VTAP_GET_CERT_WITH_ALIAS_FAILED 42011

/**
 * Successfully deleted certificate associated with the particular key alias.
 */
#define VTAP_DELETE_CERT_WITH_ALIAS_SUCCEEDED 42012

/**
 * Failed to delete certificate associated with the particular key alias.
 */
#define VTAP_DELETE_CERT_WITH_ALIAS_FAILED 42013

/**
 * Successfully obtained all the ECC key aliases.
 */
#define VTAP_GET_KEY_ALIASES_SUCCEEDED 42014

/**
 * Failed to obtain ECC key alias.
 */
#define VTAP_GET_KEY_ALIASES_FAILED 42015

/**
 * Successfully obtained all the certificate aliases.
 */
#define VTAP_GET_CERT_ALIASES_SUCCEEDED 42016

/**
 * Failed to obtain certificate aliases.
 */
#define VTAP_GET_CERT_ALIASES_FAILED 42017

/**
 * Failed to check whether the key alias exists.
 */
#define VTAP_IS_KEY_ALIAS_FAILED 42018

/**
 * Failed to check whether the certificate alias exists.
 */
#define VTAP_IS_CERT_ALIAS_FAILED 42019

/**
 * Successfully reset the token firmware.
 */
#define VTAP_RESET_TOKEN_FIRMWARE_SUCCEEDED 42020

/**
 * Failed to reset the token firmware.
 */
#define VTAP_RESET_TOKEN_FIRMWARE_FAILED 42021

/**
 * The key/certificate associated with the particular alias exists.
 */
#define VTAP_ALIAS_FOUND 42022

/**
 * The key/certificate associated with the particular alias does not exist.
 */
#define VTAP_ALIAS_NOT_FOUND 42023

/*----------- End Generic PKI APIs -----------*/

/**
 * __Cause(s):__
 * The time taken to obtain the trusted time from V-OS Smart Token Server is longer than permitted.
 * __Solution(s):__
 * Check server connection.
 * @see VTapManager.getTrustedTime:()
 */
#define VTAP_TRUSTED_TIME_TIMEOUT 42031
//#endif
