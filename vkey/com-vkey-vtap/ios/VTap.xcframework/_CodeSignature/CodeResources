<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64_armv7/VTap.framework/Headers/DistinguishedName.h</key>
		<data>
		nlvgrmnviVadBrI5HclYTNu00Zk=
		</data>
		<key>ios-arm64_armv7/VTap.framework/Headers/ResultCode.h</key>
		<data>
		ZcfP28knt3SU2tVrdeymQdfQeo4=
		</data>
		<key>ios-arm64_armv7/VTap.framework/Headers/VTap.h</key>
		<data>
		pSivbNdSklbQUP7+NDKzFDFMECg=
		</data>
		<key>ios-arm64_armv7/VTap.framework/Headers/VTapManager.h</key>
		<data>
		lwZJgVXjwrpZmEtMLCX9Hs36VFI=
		</data>
		<key>ios-arm64_armv7/VTap.framework/Info.plist</key>
		<data>
		oB+RWt/F5EKzCDdtaoA37UZhj3U=
		</data>
		<key>ios-arm64_armv7/VTap.framework/Modules/module.modulemap</key>
		<data>
		VqjlbpMZfLElRLm1f0sbfgkrYrU=
		</data>
		<key>ios-arm64_armv7/VTap.framework/PrivacyInfo.xcprivacy</key>
		<data>
		HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
		</data>
		<key>ios-arm64_armv7/VTap.framework/VTap</key>
		<data>
		4IDNFz6VWi4XYhhcq3IPNDso+qE=
		</data>
		<key>ios-arm64_armv7/VTap.framework/VTap_info.plist</key>
		<data>
		GL9F3z/uI5mele4QfXaSvmZVJS4=
		</data>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeDirectory</key>
		<data>
		aoiFauuP1RSLkAmFXe440fWQX5w=
		</data>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeRequirements</key>
		<data>
		BFxBmfFlUYrxQm9yoFQbLCfaCD8=
		</data>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		augKyRkWOURlnsYRyEtu8DHcaRQ=
		</data>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeResources</key>
		<data>
		XDGxLWLO/0ZH+a196KIezkU9YRI=
		</data>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeSignature</key>
		<data>
		U5VvLm6ftzN7N2/aiY9GqxrcCFY=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/DistinguishedName.h</key>
		<data>
		nlvgrmnviVadBrI5HclYTNu00Zk=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/ResultCode.h</key>
		<data>
		ZcfP28knt3SU2tVrdeymQdfQeo4=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/VTap.h</key>
		<data>
		pSivbNdSklbQUP7+NDKzFDFMECg=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/VTapManager.h</key>
		<data>
		lwZJgVXjwrpZmEtMLCX9Hs36VFI=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Info.plist</key>
		<data>
		wtSf4KkVf62Y6ka/5iLRMLCHps0=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Modules/module.modulemap</key>
		<data>
		VqjlbpMZfLElRLm1f0sbfgkrYrU=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/PrivacyInfo.xcprivacy</key>
		<data>
		HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/VTap</key>
		<data>
		ZskiEfkKWyeeJ2t+rjXhJdS4abA=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/VTap_info.plist</key>
		<data>
		GL9F3z/uI5mele4QfXaSvmZVJS4=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeDirectory</key>
		<data>
		z3V/4Ac3ZAOzZcK3dv0r9a7+9nI=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		fWUXRUJoDXWdzJfSJ4+71xGtwBQ=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeResources</key>
		<data>
		Nvn5rPX+2Jj37bk4p5TpRvcfzRI=
		</data>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64_armv7/VTap.framework/Headers/DistinguishedName.h</key>
		<dict>
			<key>hash</key>
			<data>
			nlvgrmnviVadBrI5HclYTNu00Zk=
			</data>
			<key>hash2</key>
			<data>
			pk5uBcbLvDXsdilrIrELqoLNGHjB5bF4HeEIKTf89+s=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/Headers/ResultCode.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZcfP28knt3SU2tVrdeymQdfQeo4=
			</data>
			<key>hash2</key>
			<data>
			pn/v16gkbG4hwkcSvbmp3Q2gtqWmrEA6AbZZRlnzxb4=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/Headers/VTap.h</key>
		<dict>
			<key>hash</key>
			<data>
			pSivbNdSklbQUP7+NDKzFDFMECg=
			</data>
			<key>hash2</key>
			<data>
			8wOI8oDsXb2vGIpsiNjrOoyyXhntJZFAWoMlBgqgXC0=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/Headers/VTapManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lwZJgVXjwrpZmEtMLCX9Hs36VFI=
			</data>
			<key>hash2</key>
			<data>
			/pfkRkqfOAY4/voTKpZbWbjHNRaVTcpRqxkmqYkn+0c=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			oB+RWt/F5EKzCDdtaoA37UZhj3U=
			</data>
			<key>hash2</key>
			<data>
			9kkT6JdAYHOmiLKPclj2kjf42kIvw5pAqE2K+ayPP9w=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			VqjlbpMZfLElRLm1f0sbfgkrYrU=
			</data>
			<key>hash2</key>
			<data>
			sdBEc3zjmASrHXjGYU/5m4GV5jgGKdyZrAKMPgZwstA=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
			</data>
			<key>hash2</key>
			<data>
			kvW/0Vum+eOfqkjK981O2CnkzdO7H09ANCWrb1jg/O0=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/VTap</key>
		<dict>
			<key>hash</key>
			<data>
			4IDNFz6VWi4XYhhcq3IPNDso+qE=
			</data>
			<key>hash2</key>
			<data>
			5ZwjxFMPRESJxJ4fwHfHMlZ/jV9P6OLeCKu7S/maZYs=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/VTap_info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			GL9F3z/uI5mele4QfXaSvmZVJS4=
			</data>
			<key>hash2</key>
			<data>
			742Y5KEiu8wHnuXe1usjx9t8qgqaRNtPQJcuolqPATk=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			aoiFauuP1RSLkAmFXe440fWQX5w=
			</data>
			<key>hash2</key>
			<data>
			uBox+TE0eXVCsAo0iiNdQg6/gKC0ozIuT2QIO5wYh90=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			BFxBmfFlUYrxQm9yoFQbLCfaCD8=
			</data>
			<key>hash2</key>
			<data>
			q16doKnDY74quX6Qu/fycgbL+XWyAflcEuFmkcXnVXk=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			augKyRkWOURlnsYRyEtu8DHcaRQ=
			</data>
			<key>hash2</key>
			<data>
			DDzr6tvgKLz/wSQ1dlSba8faZ4qLjPWbqKV2SIo/EbQ=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			XDGxLWLO/0ZH+a196KIezkU9YRI=
			</data>
			<key>hash2</key>
			<data>
			qK38E4L6fitDHNjRTjqNJybvKFKFuFfMBrhOUJ5oJys=
			</data>
		</dict>
		<key>ios-arm64_armv7/VTap.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			U5VvLm6ftzN7N2/aiY9GqxrcCFY=
			</data>
			<key>hash2</key>
			<data>
			670ci3AXpIr5Xio6LCF/9N5rD8UIevcb63+1hPbVM4w=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/DistinguishedName.h</key>
		<dict>
			<key>hash</key>
			<data>
			nlvgrmnviVadBrI5HclYTNu00Zk=
			</data>
			<key>hash2</key>
			<data>
			pk5uBcbLvDXsdilrIrELqoLNGHjB5bF4HeEIKTf89+s=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/ResultCode.h</key>
		<dict>
			<key>hash</key>
			<data>
			ZcfP28knt3SU2tVrdeymQdfQeo4=
			</data>
			<key>hash2</key>
			<data>
			pn/v16gkbG4hwkcSvbmp3Q2gtqWmrEA6AbZZRlnzxb4=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/VTap.h</key>
		<dict>
			<key>hash</key>
			<data>
			pSivbNdSklbQUP7+NDKzFDFMECg=
			</data>
			<key>hash2</key>
			<data>
			8wOI8oDsXb2vGIpsiNjrOoyyXhntJZFAWoMlBgqgXC0=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Headers/VTapManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			lwZJgVXjwrpZmEtMLCX9Hs36VFI=
			</data>
			<key>hash2</key>
			<data>
			/pfkRkqfOAY4/voTKpZbWbjHNRaVTcpRqxkmqYkn+0c=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			wtSf4KkVf62Y6ka/5iLRMLCHps0=
			</data>
			<key>hash2</key>
			<data>
			jRqfVrO/vt52D+mQovX/Y+WV/4SC0dp3g/iyHenkIhA=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			VqjlbpMZfLElRLm1f0sbfgkrYrU=
			</data>
			<key>hash2</key>
			<data>
			sdBEc3zjmASrHXjGYU/5m4GV5jgGKdyZrAKMPgZwstA=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			HVyB6E6gt5yhgwq/0zZ+FXN04Ys=
			</data>
			<key>hash2</key>
			<data>
			kvW/0Vum+eOfqkjK981O2CnkzdO7H09ANCWrb1jg/O0=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/VTap</key>
		<dict>
			<key>hash</key>
			<data>
			ZskiEfkKWyeeJ2t+rjXhJdS4abA=
			</data>
			<key>hash2</key>
			<data>
			rruZKzGoOH9OIVCGY96OGgCxXVTRObjN62pqvfhA0f0=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/VTap_info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			GL9F3z/uI5mele4QfXaSvmZVJS4=
			</data>
			<key>hash2</key>
			<data>
			742Y5KEiu8wHnuXe1usjx9t8qgqaRNtPQJcuolqPATk=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			z3V/4Ac3ZAOzZcK3dv0r9a7+9nI=
			</data>
			<key>hash2</key>
			<data>
			lBt5CzEM8kmAp555f2vSknaWWLduai5yGOjya2401Sg=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			OnX22wWFKRSOFN1+obRynMCeyXM=
			</data>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			fWUXRUJoDXWdzJfSJ4+71xGtwBQ=
			</data>
			<key>hash2</key>
			<data>
			CI8TwmfnAacbncFlJP3RArzgKGbQJpQJq4X6GciVl5o=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			Nvn5rPX+2Jj37bk4p5TpRvcfzRI=
			</data>
			<key>hash2</key>
			<data>
			fGo+CMFlxrpda1hdWiv5V1WZYlJmCt0E5VxvpXnLmc0=
			</data>
		</dict>
		<key>ios-arm64_i386_x86_64-simulator/VTap.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
