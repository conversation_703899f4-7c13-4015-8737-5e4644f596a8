#pragma once
unsigned char* pkiGetVersion(void* vmhandle);
int pkiProcessManifest(void* vmhandle, char* activationPin, int length, char* filepath);
int pkiStoreTokenPin(void* vmhandle,char* pin);
int pkiIsTokenPinRegistered(void* vmhandle);
int pkiIsTokenPinRemembered(void* vmhandle);
int pkiCheckTokenPin(void* vmhandle, char* pin, bool rememberPin);
int pkiChangeTokenPin(void* vmhandle, char* oldpin, char* newpin);
int pkiGetTokenSerial(void* vmhandle,char* tokenSerial);
int pkiSetVATag(void* vmhandle,int VATag);
int pkiInitialize(void* vmhandle);
int pkiUninitialize(void* vmhandle);
int pkiLoadTA();
int pkiUnloadTA();
int pkiLoadToken(void* vmhandle, char* tokenSerial);
int pkiUnloadToken(void* vmhandle);


//Mobile ID
int pkiGenerateEccCsr (void* vmhandle, int functionId,
		const char* inputString, int inputStringLen,
		char* pin, bool rememberPin,
		unsigned char* output, int* outputLen);
int pkiIsMobileIdPinRemembered(void* vmhandle, int functionId);
int pkiCheckMobileIdPin(void* vmhandle, int functionId, char* pin, bool rememberPin);
int pkiChangeMobileIdPin(void* vmhandle, int functionId, char* oldpin, char* newpin);
int pkiEccSha256Sign (void* vmhandle, int functionId, const char* msg, int msgSize,
		unsigned char* signedMsg, int* signedMsgSize);
int pkiRemoveEccKeyPair(void* vmhandle, int functionId);
int pkiDecryptMsg(void* vmhandle, int functionId, const char* msg, int msgSize,
		unsigned char* plainMsg, int* plainMsgSize);

int pkiVerifySignature(void* vmhandle, int functionId, unsigned char* keyDer, int sizeKeyDer);
int pkiReplacePreGenKeyWithActiveAuthKey(void* vmhandle);

/* Netrust */
int generateEccCsrWithAlias(char* inputString, int inputStringLen, char* output, int* outputLen, int keyAlias);
int deleteEccKeyWithAlias(int keyAlias);
int eccSignWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int eccSha1SignWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int eccSha256SignWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int eccVerifyWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int eccSha1VerifyWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int eccSha256VerifyWithAlias(unsigned char* inputData, int inputDataLen, unsigned char* signature, int signatureLen, int keyAlias);
int addCertWithAlias(unsigned char* userCert, int userCertLen, unsigned char* intermediateCert, int intermediateCertLen, unsigned char* rootCert, int rootCertLen, int certAlias);
int getCertWithAlias(unsigned char* outputUserCert, int* outputUserCertLen, unsigned char* outputIntermediateCert, int* outputIntermediateCertLen, unsigned char* outputRootCert, int* outputRootCertLen, int certAlias);
int deleteCertWithAlias(int certAlias);
int isKeyAlias(int keyAlias);
int isCertAlias(int certAlias);
int getKeyAliases(int* keyAliases, int keyAliasesLen);
int getCertAliases(int* certAliases, int certAliasesLen);
int resetTokenFirmware();
/* End Netrust */
