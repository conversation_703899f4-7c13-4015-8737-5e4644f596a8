<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/authenticator_interface.h</key>
		<data>
		/NRJYRfWveO//umDS8GwcOSGQ6o=
		</data>
		<key>Headers/authenticator_interface_return_code.h</key>
		<data>
		exC2T4EkiMSe/PON5FWYic6aZKg=
		</data>
		<key>Headers/pkiTAInterface.h</key>
		<data>
		n82EFk5rWKiQmRnl0SJRKvs8FeY=
		</data>
		<key>Info.plist</key>
		<data>
		A2InZtHKvYRMlyJfEMnk2upIqoI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/authenticator_interface.h</key>
		<dict>
			<key>hash</key>
			<data>
			/NRJYRfWveO//umDS8GwcOSGQ6o=
			</data>
			<key>hash2</key>
			<data>
			OcbIm0dsZLdpQeZ8QFrY73+nFuFyyArIjB+dr6/cZmQ=
			</data>
		</dict>
		<key>Headers/authenticator_interface_return_code.h</key>
		<dict>
			<key>hash</key>
			<data>
			exC2T4EkiMSe/PON5FWYic6aZKg=
			</data>
			<key>hash2</key>
			<data>
			0ywtaiJ0WgfVhR9UEgUiki1PQHqz+Ikv+NVPXSLQnCQ=
			</data>
		</dict>
		<key>Headers/pkiTAInterface.h</key>
		<dict>
			<key>hash</key>
			<data>
			n82EFk5rWKiQmRnl0SJRKvs8FeY=
			</data>
			<key>hash2</key>
			<data>
			edBmtgVL0ovGrCOugoa0tZkGlMVQpSHGrEf2VLydU20=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
