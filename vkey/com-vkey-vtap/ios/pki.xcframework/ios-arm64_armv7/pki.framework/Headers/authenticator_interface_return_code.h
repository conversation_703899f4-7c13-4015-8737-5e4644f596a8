#ifndef AUTHENTICATOR_INTERFACE_RETURN_CODE_H
#define AUTHENTICATOR_INTERFACE_RETURN_CODE_H

typedef enum AuthenticatorInterfaceReturnCode {
    CTAP2_OK                                = 0x00,
    CTAP2_ERR_CBOR_UNEXPECTED_TYPE          = 0x11,
    CTAP2_ERR_INVALID_CBOR                  = 0x12,
    CTAP2_ERR_MISSING_PARAMETER             = 0x14,
    CTAP2_ERR_CREDENTIAL_EXCLUDED           = 0x19,
    CTAP2_ERR_UNSUPPORTED_ALGORITHM         = 0x26,
    CTAP2_ERR_OPERATION_DENIED              = 0x27,
    CTAP2_ERR_INVALID_OPTION                = 0x2C,
    CTAP2_ERR_NO_CREDENTIALS                = 0x2E,
    CTAP2_ERR_REQUEST_TOO_LARGE             = 0x39,
    
    AUTHENTICATOR_UP_IS_REQUIRED            = -5000,
    AUTHENTICATOR_UV_IS_REQUIRED            = -5001,
    AUTHENTICATOR_CREDENTIAL_ID_IS_REQUIRED = -5002,
    
    AUTHENTICATOR_INVALID_SEQUENCE          = -5003,
    AUTHENTICATOR_CBOR_CREATE_FAILED        = -5004
} AuthenticatorInterfaceReturnCode;

#endif /* AUTHENTICATOR_INTERFACE_RETURN_CODE_H */
