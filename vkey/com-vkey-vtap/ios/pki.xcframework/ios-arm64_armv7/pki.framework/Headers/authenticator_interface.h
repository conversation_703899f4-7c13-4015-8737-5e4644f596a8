#ifndef AUTHENTICATOR_INTERFACE_H
#define AUTHENTICATOR_INTERFACE_H

typedef enum ObjectId {
    RP_ID                       = 0x01,
    RP_NAME                     = 0x02,
    RP_ICON                     = 0x03,
    USER_ID                     = 0x04,
    USER_DISPLAY_NAME           = 0x05,
    USER_NAME                   = 0x06,
    USER_ICON                   = 0x07,
    AUTHENTICATOR_DATA          = 0x08,
    ATTESTATION_STATEMENT_ALG   = 0x09,
    ATTESTATION_STATEMENT_SIG   = 0x0a,
    ATTESTATION_STATEMENT_X5C   = 0x0b,
    CREDENTIAL_ID               = 0x0c,
    ASSERTION_SIGNATURE         = 0x0d,
    CREDENTIAL_METADATA         = 0x0e
} ObjectId;

int authenticator_set_user_presence(unsigned int up);

int authenticator_set_user_verification(unsigned int uv);

int authenticator_set_credential_id(const unsigned char *credential_id,
                                    unsigned int credential_id_size);

int authenticator_make_credential_set(const unsigned char *request,
                                      unsigned int request_size);

int authenticator_make_credential_get(unsigned char *response_buffer,
                                      unsigned int *response_buffer_size);

int authenticator_get_assertion_set(const unsigned char *request,
                                    unsigned int request_size);

int authenticator_get_assertion_get(unsigned char *response_buffer,
                                    unsigned int *response_buffer_size);

int authenticator_reset(void);

#endif /* AUTHENTICATOR_INTERFACE_H */
