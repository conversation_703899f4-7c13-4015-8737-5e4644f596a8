<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Commons.h</key>
		<data>
		DgF1L91ksp2rv1sUhRqcyS0ZRio=
		</data>
		<key>Headers/FileInputStream.h</key>
		<data>
		IpJt6G0aYtrPOos/vsmSEbKLtVM=
		</data>
		<key>Headers/FileOutputStream.h</key>
		<data>
		KvdOh5iLo/2QvwKwzBGcuE3Bfbg=
		</data>
		<key>Headers/SecureData.h</key>
		<data>
		PQJXE0KXOmQ8tLma+I5/utd5Jy8=
		</data>
		<key>Headers/SecureDatabase.h</key>
		<data>
		qucszJCR7VnQcug6u6tfLGpksdc=
		</data>
		<key>Headers/SecureFile.h</key>
		<data>
		tENf4NqqH2QeFsHC93Nj54ygFP0=
		</data>
		<key>Headers/SecureFileIO.h</key>
		<data>
		oRW5el2btAOBhrO42lopx/lavA4=
		</data>
		<key>Headers/sqlite3.h</key>
		<data>
		xC7GsOb9EhArn8yGtuZEbt0LO18=
		</data>
		<key>Headers/sqlite3_secure.h</key>
		<data>
		B8ZMb6lOk4IcWgEU7pBvURS0AH0=
		</data>
		<key>Info.plist</key>
		<data>
		BmI0iP7UuIZ/6JPb+WkvpA78w9I=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		15iGWNx0lR8y4BX70bUVazE2GXI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Commons.h</key>
		<dict>
			<key>hash</key>
			<data>
			DgF1L91ksp2rv1sUhRqcyS0ZRio=
			</data>
			<key>hash2</key>
			<data>
			Yh6AYqkXteZ8eRMxdHuj9NXcB+LTEe5umdwFGE+tBzs=
			</data>
		</dict>
		<key>Headers/FileInputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			IpJt6G0aYtrPOos/vsmSEbKLtVM=
			</data>
			<key>hash2</key>
			<data>
			Cs+ddJUoJQYBtFYsvobeyZDdIBXD0Mac0vaAEKrmrJ8=
			</data>
		</dict>
		<key>Headers/FileOutputStream.h</key>
		<dict>
			<key>hash</key>
			<data>
			KvdOh5iLo/2QvwKwzBGcuE3Bfbg=
			</data>
			<key>hash2</key>
			<data>
			KPduOqmWcMlHmPYFE19tk+VEJUuK9g/yVtBZvwGxXys=
			</data>
		</dict>
		<key>Headers/SecureData.h</key>
		<dict>
			<key>hash</key>
			<data>
			PQJXE0KXOmQ8tLma+I5/utd5Jy8=
			</data>
			<key>hash2</key>
			<data>
			4O697hTBs+NKA/80x/Zdtsa0boRY4T2j8WtxklsouC0=
			</data>
		</dict>
		<key>Headers/SecureDatabase.h</key>
		<dict>
			<key>hash</key>
			<data>
			qucszJCR7VnQcug6u6tfLGpksdc=
			</data>
			<key>hash2</key>
			<data>
			wHXw9mi+ms5P+rz7SFHZ/N9JXsvOLdrflZc64ZDT/Ew=
			</data>
		</dict>
		<key>Headers/SecureFile.h</key>
		<dict>
			<key>hash</key>
			<data>
			tENf4NqqH2QeFsHC93Nj54ygFP0=
			</data>
			<key>hash2</key>
			<data>
			8gIFi0mKEGzBL8I41A/F4dM/+R4NXTEc8S6gq3MpA10=
			</data>
		</dict>
		<key>Headers/SecureFileIO.h</key>
		<dict>
			<key>hash</key>
			<data>
			oRW5el2btAOBhrO42lopx/lavA4=
			</data>
			<key>hash2</key>
			<data>
			wSNn3c3FG5AlZBLVotDVhQwVPPssWP5PkQLnOMlkqxY=
			</data>
		</dict>
		<key>Headers/sqlite3.h</key>
		<dict>
			<key>hash</key>
			<data>
			xC7GsOb9EhArn8yGtuZEbt0LO18=
			</data>
			<key>hash2</key>
			<data>
			/MwneN/YSyWWGPuNQ5gbrPn1bHUPnfwnT0Ia95/ISnY=
			</data>
		</dict>
		<key>Headers/sqlite3_secure.h</key>
		<dict>
			<key>hash</key>
			<data>
			B8ZMb6lOk4IcWgEU7pBvURS0AH0=
			</data>
			<key>hash2</key>
			<data>
			Vl7Cr2ATr2ScHFDmaTNER/h4xj/A9EHL62qzpgHJBk0=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			15iGWNx0lR8y4BX70bUVazE2GXI=
			</data>
			<key>hash2</key>
			<data>
			pm0l+ifpWVyWWx4l+KINxDjeJUpy1K3QLzNhsVLSPHM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
