/**
 * @file   SecureFileIO.h
 * <AUTHOR>
 * @date   2/6/17
 *
 * Copyright © 2017-2019 V-Key. All rights reserved.
 *
 */

#pragma once
#import <Foundation/Foundation.h>

/**
 * @brief Utility class for byte and file encryption.
 */
@interface SecureFileIO : NSObject

/**
 * @brief   Encrypt data using CBC mode.
 *
 * @param[in]   data The data to be encrypted.
 * @param[out]  output The encrypted data.
 * @return      A negative error code or SFIO_OK which means no error.
 */
+ (int) encryptData:(NSData *)data
             output:(NSMutableData *)outputData;
/**
 * @brief   Encrypt data to file using XTS mode.
 *
 * @param[in] data      The data to be encrypted into a file.
 * @param[in] path      The path to the output encrypted file.
 * @param[in] password  The password (optional). If provided, it must be a valid password.
 * A valid password, must contain:
 * @li at least one digit
 * @li at least one lowercase character
 * @li at least one uppercase character
 * @li at least one symbols from !@#$%^&
 * @li at least with a length of 6 up to a maximum of 20
 * @param[in] useAuxiliaryFile Whether to write to a temporary file before reflecting changes to path. Set to `YES` or `NO`.
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) encryptData:(NSData*)data
              toFile:(NSString *)path
            password:(NSString *)password
          atomically:(BOOL)useAuxiliaryFile;
/**
 * @brief Decrypt data using CBC mode.
 *
 * @param[in]  cipher  The ciper to be decrypted.
 * @param[out] output  The decrypted data.
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) decryptData:(NSData *)cipher
             output:(NSMutableData *)normal;
/**
 * @brief Encrypt string to file using XTS mode.
 *
 * @param[in]   str         The string to be encrypted into a file.
 * @param[in]   path        The path to the output encrypted file.
 * @param[in]   password    The password (optional). If provided, it must be a valid password.
 * A valid password, must contain:
 * @li at least one digit
 * @li at least one lowercase character
 * @li at least one uppercase character
 * @li at least one symbols from !@#$%^&
 * @li at least with a length of 6 up to a maximum of 20
 *
 * @param[in] useAuxiliaryFile if 'yes' would write to a temporary file before reflecting changes to path
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) encryptString:(NSString*)str
                toFile:(NSString *)path
              password:(NSString *)password
            atomically:(BOOL)useAuxiliaryFile;

/**
 * @brief Using XTS mode to decrypt a file and converted to string format
 *
 * @param[in]   path        The path to the encrypted file.
 * @param[in]   password    The password (optional). If provided, it must be a valid password.
 * @param[out]  output   The decrypted string.
 * A valid password, must contain:
 * @li at least one digit
 * @li at least one lowercase character
 * @li at least one uppercase character
 * @li at least one symbols from !@#$%^&
 * @li at least with a length of 6 up to a maximum of 20
 *
 * @return A negative error code or SFIO_OK which means no error.
 */

+ (int) decryptStringFromFile:(NSString *)path
                     password:(NSString *)password
                       output:(NSMutableString *)decryptedString;
/**
 * @brief   Decrypt a file at given path using XTS mode.
 *
 * @param[in]   path        The path to the encrypted file.
 * @param[in]   password     The password (optional). If provided, it must be a valid password.
 * @param[out]  output  The decrypted data.
 * A valid password, must contain:
 * @li at least one digit
 * @li at least one lowercase character
 * @li at least one uppercase character
 * @li at least one symbols from !@#$%^&
 * @li at least with a length of 6 up to a maximum of 20
 *
 * @return A negative error code or SFIO_OK which means no error.
 */

+ (int) decryptFile:(NSString *)path
           password:(NSString *)password
             output:(NSMutableData *)outputData;
/**
 * @brief   Update the a file at \p path from \p oldPassword to \p newPassword.
 *
 * @param[in]  path         The file path.
 * @param[in]  newPassword  The new password.
 * @param[in]  oldPassword  The old password.
 *
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) updateFile:(NSString *)path
       newPassword:(NSString *)newPassword
       oldPassword:(NSString *)oldPassword;

/**
 * @brief      Encrypt a file at \p path with \p newPassword.
 *
 * @param[in]  path         The file path.
 * @param[in]  newPassword  The new password.
 *
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) encryptFile:(NSString *)path
        newPassword:(NSString *)newPassword;

/**
 * @brief      Encrypt a file \p from one path and save it \p to another path with \p newPassword.
 *
 * @param[in]  from         The source path.
 * @param[in]  to           The destination path.
 * @param[in]  newPassword  The new password.
 *
 * @return A negative error code or SFIO_OK which means no error.
 */
+ (int) encryptFileFrom:(NSString *)from
                     to:(NSString *)to
            newPassword:(NSString *)newPassword;

@end
