/**
 * @file   SecureData.h
 * <AUTHOR>
 * @date   2/6/17
 *
 * Copyright © 2017 V-Key. All rights reserved.
 *
 */
#pragma once
#include "Commons.h"

namespace securefileio { namespace core {


    /**
     * @brief   Class for handling secure data.
     */
    class SecureData {
        SecureData() = delete;
    public:
    	/**
    	 * @brief  Encrypt \p input plain data of \p len into \p output and set \p outputLen.
    	 *
    	 * @param[in]  input      The input plain data.
    	 * @param[in]  len        The length of input data.
    	 * @param[in]  output     The output encrypted data.
    	 * @param[in]  outputLen  The length of output data.
         * @return A negative error code or the length of the data that would have been copied onto \p output if \p outputLen was sufficiently large enough and \p output is not 'null'
    	 */
        SFIO_API static int_t encrypt(const void *input, uint_t len, void *output, uint_t outputLen);

        /**
         * @brief   Decrypt \p input cipher data of \p len into \p output and set \p outputLen.
         *
         * @param[in]  input      The input ciper data.
         * @param[in]  len        The length of input data.
         * @param[in]  output     The output decrypted data.
         * @param[in]  outputLen  The length of output data.
         * @return A negative error code or the length of the data that would have been copied onto \p output if \p outputLen was sufficiently large enough and \p output is not 'null'
         */
        SFIO_API static int_t decrypt(const void *input, uint_t len, void *output, uint_t outputLen);
    };
}}
