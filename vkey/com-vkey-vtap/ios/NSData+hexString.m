//
//  NSData+hexString.m
//  TestQuitting
//
//  Created by V-Key  on 6/4/15.
//  Copyright (c) 2015 V-Key Pte Ltd. All rights reserved.
//

#import "NSData+hexString.h"

@implementation NSData (hexString)

#pragma mark - String Conversion

- (NSString *)hexadecimalString {
    /* Returns hexadecimal string of NSData. Empty string if data is empty.   */
    
    const unsigned char *dataBuffer = (const unsigned char *)[self bytes];
    
    if (!dataBuffer)
        return [NSString string];
    
    NSUInteger          dataLength  = [self length];
    NSMutableString     *hexString  = [NSMutableString stringWithCapacity:(dataLength * 2)];
    
    for (int i = 0; i < dataLength; ++i)
        [hexString appendString:[NSString stringWithFormat:@"%02lx", (unsigned long)dataBuffer[i]]];
    
    return [NSString stringWithString:hexString];
}

@end
