// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		3807FB2A22956BB200CE0A04 /* ResultCodeModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 3807FB2322956BB100CE0A04 /* ResultCodeModule.m */; };
		3807FB2C22956BB200CE0A04 /* NSData+hexString.m in Sources */ = {isa = PBXBuildFile; fileRef = 3807FB2822956BB200CE0A04 /* NSData+hexString.m */; };
		38B1843F2297A15C00153C8F /* VKSecureKeypad.m in Sources */ = {isa = PBXBuildFile; fileRef = 38B1843D2297A15C00153C8F /* VKSecureKeypad.m */; };
		8C3B0FE12DB2734A00393215 /* VosWrapper.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FDF2DB2734A00393215 /* VosWrapper.xcframework */; };
		8C3B0FE22DB2734A00393215 /* otp.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FDB2DB2734A00393215 /* otp.xcframework */; };
		8C3B0FE32DB2734A00393215 /* pki.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FDC2DB2734A00393215 /* pki.xcframework */; };
		8C3B0FE42DB2734A00393215 /* securefileio.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FDD2DB2734A00393215 /* securefileio.xcframework */; };
		8C3B0FE52DB2734A00393215 /* VTap.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FE02DB2734A00393215 /* VTap.xcframework */; };
		8C3B0FE62DB2734A00393215 /* VGuard.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C3B0FDE2DB2734A00393215 /* VGuard.xcframework */; };
		8C3B0FE82DB2742D00393215 /* dummy.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C3B0FE72DB2742D00393215 /* dummy.swift */; };
		8C3B0FEC2DB51F9D00393215 /* SFIO.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C3B0FEB2DB51F9D00393215 /* SFIO.m */; };
		8C3B0FEF2DB62F7B00393215 /* Utils.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C3B0FEE2DB62F7B00393215 /* Utils.m */; };
		B3E7B58A1CC2AC0600A0062D /* VTapPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = B3E7B5891CC2AC0600A0062D /* VTapPlugin.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		58B511D91A9E6C8500147676 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		134814201AA4EA6300B7C361 /* libRNVosSmarttoken.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNVosSmarttoken.a; sourceTree = BUILT_PRODUCTS_DIR; };
		3807FB2222956BB100CE0A04 /* NSData+hexString.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+hexString.h"; sourceTree = "<group>"; };
		3807FB2322956BB100CE0A04 /* ResultCodeModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ResultCodeModule.m; sourceTree = "<group>"; };
		3807FB2722956BB200CE0A04 /* ResultCodeModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ResultCodeModule.h; sourceTree = "<group>"; };
		3807FB2822956BB200CE0A04 /* NSData+hexString.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+hexString.m"; sourceTree = "<group>"; };
		38B1843D2297A15C00153C8F /* VKSecureKeypad.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VKSecureKeypad.m; sourceTree = "<group>"; };
		38B1843E2297A15C00153C8F /* VKSecureKeypad.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VKSecureKeypad.h; sourceTree = "<group>"; };
		8C3B0FDB2DB2734A00393215 /* otp.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = otp.xcframework; sourceTree = "<group>"; };
		8C3B0FDC2DB2734A00393215 /* pki.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = pki.xcframework; sourceTree = "<group>"; };
		8C3B0FDD2DB2734A00393215 /* securefileio.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = securefileio.xcframework; sourceTree = "<group>"; };
		8C3B0FDE2DB2734A00393215 /* VGuard.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:63DA3SWC73:V-Key Pte Ltd"; lastKnownFileType = wrapper.xcframework; path = VGuard.xcframework; sourceTree = "<group>"; };
		8C3B0FDF2DB2734A00393215 /* VosWrapper.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = VosWrapper.xcframework; sourceTree = "<group>"; };
		8C3B0FE02DB2734A00393215 /* VTap.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:63DA3SWC73:V-Key Pte Ltd"; lastKnownFileType = wrapper.xcframework; path = VTap.xcframework; sourceTree = "<group>"; };
		8C3B0FE72DB2742D00393215 /* dummy.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = dummy.swift; sourceTree = "<group>"; };
		8C3B0FE92DB2742E00393215 /* RNVosSmarttoken-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "RNVosSmarttoken-Bridging-Header.h"; sourceTree = "<group>"; };
		8C3B0FEA2DB51F9D00393215 /* SFIO.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SFIO.h; sourceTree = "<group>"; };
		8C3B0FEB2DB51F9D00393215 /* SFIO.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SFIO.m; sourceTree = "<group>"; };
		8C3B0FED2DB62F7B00393215 /* Utils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = Utils.h; sourceTree = "<group>"; };
		8C3B0FEE2DB62F7B00393215 /* Utils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = Utils.m; sourceTree = "<group>"; };
		B3E7B5881CC2AC0600A0062D /* VTapPlugin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VTapPlugin.h; sourceTree = "<group>"; };
		B3E7B5891CC2AC0600A0062D /* VTapPlugin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VTapPlugin.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		58B511D81A9E6C8500147676 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8C3B0FE12DB2734A00393215 /* VosWrapper.xcframework in Frameworks */,
				8C3B0FE22DB2734A00393215 /* otp.xcframework in Frameworks */,
				8C3B0FE32DB2734A00393215 /* pki.xcframework in Frameworks */,
				8C3B0FE42DB2734A00393215 /* securefileio.xcframework in Frameworks */,
				8C3B0FE52DB2734A00393215 /* VTap.xcframework in Frameworks */,
				8C3B0FE62DB2734A00393215 /* VGuard.xcframework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		134814211AA4EA7D00B7C361 /* Products */ = {
			isa = PBXGroup;
			children = (
				134814201AA4EA6300B7C361 /* libRNVosSmarttoken.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		58B511D21A9E6C8500147676 = {
			isa = PBXGroup;
			children = (
				8C3B0FE72DB2742D00393215 /* dummy.swift */,
				38B1843E2297A15C00153C8F /* VKSecureKeypad.h */,
				38B1843D2297A15C00153C8F /* VKSecureKeypad.m */,
				3807FB2222956BB100CE0A04 /* NSData+hexString.h */,
				3807FB2822956BB200CE0A04 /* NSData+hexString.m */,
				8C3B0FED2DB62F7B00393215 /* Utils.h */,
				8C3B0FEE2DB62F7B00393215 /* Utils.m */,
				3807FB2722956BB200CE0A04 /* ResultCodeModule.h */,
				3807FB2322956BB100CE0A04 /* ResultCodeModule.m */,
				8C3B0FEA2DB51F9D00393215 /* SFIO.h */,
				8C3B0FEB2DB51F9D00393215 /* SFIO.m */,
				B3E7B5881CC2AC0600A0062D /* VTapPlugin.h */,
				B3E7B5891CC2AC0600A0062D /* VTapPlugin.m */,
				134814211AA4EA7D00B7C361 /* Products */,
				60C8411122A7D61A006E8140 /* Frameworks */,
				8C3B0FE92DB2742E00393215 /* RNVosSmarttoken-Bridging-Header.h */,
			);
			sourceTree = "<group>";
		};
		60C8411122A7D61A006E8140 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8C3B0FDB2DB2734A00393215 /* otp.xcframework */,
				8C3B0FDC2DB2734A00393215 /* pki.xcframework */,
				8C3B0FDD2DB2734A00393215 /* securefileio.xcframework */,
				8C3B0FDE2DB2734A00393215 /* VGuard.xcframework */,
				8C3B0FDF2DB2734A00393215 /* VosWrapper.xcframework */,
				8C3B0FE02DB2734A00393215 /* VTap.xcframework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		58B511DA1A9E6C8500147676 /* RNVosSmarttoken */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNVosSmarttoken" */;
			buildPhases = (
				58B511D71A9E6C8500147676 /* Sources */,
				58B511D81A9E6C8500147676 /* Frameworks */,
				58B511D91A9E6C8500147676 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNVosSmarttoken;
			productName = RCTDataManager;
			productReference = 134814201AA4EA6300B7C361 /* libRNVosSmarttoken.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58B511D31A9E6C8500147676 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0830;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					58B511DA1A9E6C8500147676 = {
						CreatedOnToolsVersion = 6.1.1;
						LastSwiftMigration = 1600;
					};
				};
			};
			buildConfigurationList = 58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNVosSmarttoken" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 58B511D21A9E6C8500147676;
			productRefGroup = 58B511D21A9E6C8500147676;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58B511DA1A9E6C8500147676 /* RNVosSmarttoken */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		58B511D71A9E6C8500147676 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B3E7B58A1CC2AC0600A0062D /* VTapPlugin.m in Sources */,
				8C3B0FE82DB2742D00393215 /* dummy.swift in Sources */,
				38B1843F2297A15C00153C8F /* VKSecureKeypad.m in Sources */,
				3807FB2A22956BB200CE0A04 /* ResultCodeModule.m in Sources */,
				8C3B0FEC2DB51F9D00393215 /* SFIO.m in Sources */,
				8C3B0FEF2DB62F7B00393215 /* Utils.m in Sources */,
				3807FB2C22956BB200CE0A04 /* NSData+hexString.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		58B511ED1A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		58B511EE1A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58B511F01A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Framew",
					"$(PROJECT_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNVosSmarttoken;
				SKIP_INSTALL = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "RNVosSmarttoken-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		58B511F11A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Framew",
					"$(PROJECT_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNVosSmarttoken;
				SKIP_INSTALL = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "RNVosSmarttoken-Bridging-Header.h";
				SWIFT_VERSION = 6.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "RNVosSmarttoken" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511ED1A9E6C8500147676 /* Debug */,
				58B511EE1A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "RNVosSmarttoken" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511F01A9E6C8500147676 /* Debug */,
				58B511F11A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 58B511D31A9E6C8500147676 /* Project object */;
}
