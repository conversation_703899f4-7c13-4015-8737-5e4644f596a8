//
//  VTapPlugin.m
//  RNVosSmarttokenReactNative
//
//  Created by Tu 04/18/2025.
//  Copyright (c) 2025 V-Key Pte Ltd. All rights reserved.
//
#import "VTapPlugin.h"
#import <React/RCTLog.h>
#import "React/RCTBridgeModule.h"

#define VOS_SUCCESS                40200
#define VOS_INIT                   0
#define ERROR                     -1
#define ERROR_mess                @"VGuard is nil"
#define ERROR_mess_vtap           @"VTap is nil"

#define VGUARD_EVENTS             @"VGUARD_EVENTS"
#define VTAP_EVENTS               @"VTAP_EVENTS"
#define VGUARD_ERROR              @"VGUARD_ERROR"
#define VOS_READY                 @"VOS_READY"
#define VGUARD_PROFILE_LOADED     @"VGUARD_PROFILE_LOADED"
#define VGUARD_READY              @"VGUARD_READY"

#define ACTION_FINISH             @"ACTION_FINISH"
#define ACTION_SCAN_COMPLETE      @"ACTION_SCAN_COMPLETE"
#define VOS_READY                 @"VOS_READY"

#define VTAP_SETUP_ACTION         @"VTAP_SETUP_ACTION"

#define VGUARD_SSL_ERROR_DETECTED @"VGUARD_SSL_ERROR_DETECTED"
#define VGUARD_SCREEN_SHARING_DETECTED @"VGUARD_SCREEN_SHARING_DETECTED"


@interface VTapPlugin () {
    VGuardManager *vguardMgr;
    NSString *mTiUrl;
    NSString *mTlaUrl;
    NSInteger vosFirmwareCode;
    BOOL *sslPinning;
    
    VTapManager *vtapMgr;
    
    NSString* tiServer;
    NSString* provisioningServer;
    NSString* vtapServer;
    NSString* pkiServer;
    NSString* ts;
    NSString* apin;
}
@end

@implementation VTapPlugin
RCT_EXPORT_MODULE();

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


- (NSDictionary *)constantsToExport
{
    return @{
        VGUARD_EVENTS           : VGUARD_EVENTS,
        VTAP_EVENTS             :  VTAP_EVENTS,
        VGUARD_READY            : VGUARD_READY,
        VGUARD_SSL_ERROR_DETECTED : VGUARD_SSL_ERROR_DETECTED,
        VGUARD_ERROR            : VGUARD_ERROR,
        VOS_READY               : VOS_READY,
        VGUARD_PROFILE_LOADED   : VGUARD_PROFILE_LOADED,
        ACTION_FINISH           : ACTION_FINISH,
        ACTION_SCAN_COMPLETE    : ACTION_SCAN_COMPLETE,
        VGUARD_SCREEN_SHARING_DETECTED : VGUARD_SCREEN_SHARING_DETECTED,
        VTAP_SETUP_ACTION      : VTAP_SETUP_ACTION,
    };
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;
}

- (NSArray<NSString *> *)supportedEvents
{
    return @[VGUARD_EVENTS, VTAP_EVENTS, VGUARD_READY, VGUARD_ERROR, VOS_READY, VGUARD_SSL_ERROR_DETECTED, ACTION_FINISH, ACTION_SCAN_COMPLETE, VTAP_SETUP_ACTION];
}

-(void)sendEvent:(NSString *)eventName param:(NSObject*)params{
    NSDictionary *emitBody = @{@"action": eventName, @"data": params};
    [self sendEventWithName:VGUARD_EVENTS body:emitBody];
}

-(void)sendEventVtap:(NSString *)eventName param:(NSObject*)params{
    NSDictionary *emitBody = @{@"action": eventName, @"data": params};
    [self sendEventWithName:VTAP_EVENTS body:emitBody];
}

- (UIViewController *)topMostViewController {
    UIViewController *topController = [UIApplication sharedApplication].keyWindow.rootViewController;
    
    // Navigate to the top-most presented view controller
    while (topController.presentedViewController) {
        topController = topController.presentedViewController;
    }
    return topController;
}

- (void) excludeFilesFromIOSBackup {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSURL *documentsURL = [[fileManager URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] firstObject];
    
    NSArray *files = @[@"IRK.enc", @"trust.vos", @"trust.key", @"talk.vos", @"SignerDatastore", @"firmware", @"profile", @"signature", @"vkeylicensepack", @"voscodesign.vky"];
    
    for (NSString *file in files) {
        NSURL *fileURL = [documentsURL URLByAppendingPathComponent:file];
        
        NSError *error = nil;
        BOOL success = [fileURL setResourceValue:@(YES) forKey:NSURLIsExcludedFromBackupKey error:&error];
    }
    NSLog(@"Done excludeFilesFromIOSBackup");
}

#pragma mark - VGuardThreatsDelegate

- (void)vGuardScan:(NSArray*)threatsArray {
    NSLog(@"^vGuardScan scan");
    
    NSMutableArray *threats = [[NSMutableArray alloc] init];
    for (NSDictionary* thr in threatsArray) {
        NSString *thClass = thr[@"threatTypeId"];
        NSString *thInfo = thr[@"info"];
        NSString *thName = thr[@"name"];
        NSDictionary *dic = @{@"ThreatClass": thClass, @"ThreatInfo": thInfo, @"ThreatName": thName};
        [threats addObject:dic];
    }
    [self sendEvent:ACTION_SCAN_COMPLETE param:threats];
}

- (void)vGuardDidDetectScreenSharing {
    NSLog(@"^vGuardDidDetectScreenSharing");
//    [self showPopupWithTitle:@"" message:@"" quit:YES after:5];
    [self sendEvent:VGUARD_SCREEN_SHARING_DETECTED param:@"yes"];
}

- (void)vGuardDidDetectThreats:(NSDictionary *)threatsInfo {
    NSLog(@"^vGuardDidDetectThreats");
}

#pragma mark - VGuardManagerProtocol

- (void)vGuardDidDetectSSLError:(NSError *)error {
    [self sendEvent:VGUARD_SSL_ERROR_DETECTED param:error.userInfo];
}

- (void)statusVGuard:(VGUARD_STATUS)status withError:(NSError *)error {
    NSLog(@"^VGUARD_READY");
    //    [self sendEvent:VGUARD_READY param: [NSNumber numberWithInt:status]];
}

- (void)statusVOS:(VOS_STATUS)status withError:(NSError *)error {
    if (status==VOS_OK) {
        vosFirmwareCode = VOS_SUCCESS; // success
        // [vguardMgr start];
        
    }
    
    NSNumber *returnCode = [NSNumber numberWithInt:status];
    if(error != nil) {
        vosFirmwareCode = error.code;
        returnCode = [NSNumber numberWithLong:error.code];
        [VosWrapper forceSyncLogs];
        if(error.code == -8 || error.code == -5 || error.code == -3) {
            [vguardMgr resetVOSTrustedStorage];
            NSLog(@"auto trigger resetVOSTrustedStorage");
//            [self triggerRestartVguard];
            NSLog(@"auto trigger re-start");
        }
        
        // Handler error and quit App in plugin
        if(error.code == -1039 || error.code == 20050 || error.code < -999) {
            // quit app
        }
    }
    NSLog(@"^statusVOS: %@", returnCode);
    
    [self sendEvent:VOS_READY param:returnCode ];
}

- (void)vGuardDidFinishInitializing:(BOOL)status withError:(NSError *)error {
    NSLog(@"vGuardDidFinishInitializing: %@", status ? @"SUCCESS" : @"FAILED");
    NSNumber *returnCode = [NSNumber numberWithInt:status];
    if(error != nil) {
        returnCode = [NSNumber numberWithLong:error.code];
        [VosWrapper forceSyncLogs];
        if (error.code >= 20000 && error.code < 30000) {
            // quit app
        } else if(error.code == 20035) {
            // ignore 20035 since it was handled in statusVOS callback (-8)
        } else {
            // [self sendEvent:VGUARD_ERROR param: returnCode];
        }
    }
    NSLog(@"^vGuardDidFinishInitializing: %@", returnCode);
    [self sendEvent:VGUARD_READY param: returnCode];
}

#pragma mark - VGuardExceptionHandlerProtocol

- (void)vGuardExceptionHandler:(NSException *)exception {
    NSLog(@"vGuardExceptionHandler: %@", exception);
    // [self sendEvent:VGUARD_ERROR param: exception.description];
}

- (void)vGuardCrashDetected {
    NSLog(@"vGuardCrashDetected");
}

#pragma mark - VTap

- (void)didStartVTap:(int)statusCode {
    NSLog(@"^didStartVTap: %d", statusCode);
    
    // handle:40207 VOS_FAILED_WITH_TRUST_STORAGE_ERROR, 40204 VTAP_TOKEN_LOAD_FAILED
    
    if (statusCode == VOS_FAILED_WITH_TRUST_STORAGE_ERROR) {
        [vtapMgr resetVOSTrustedStorage];
        [vtapMgr onDestroy];
        [self vSetupVtap];
    } else if (statusCode == VTAP_TOKEN_LOAD_FAILED) {
        // remove all token
        NSArray<NSString *> *allTokens = [vtapMgr getAllProvisionedTokens];
        for (NSString *token in allTokens) {
            [vtapMgr removeTokenFirmware:token];
        }
        NSNumber *returnCode = [NSNumber numberWithInt:statusCode];
        [self sendEventVtap:VTAP_SETUP_ACTION param:returnCode ];
    } else {
        NSNumber *returnCode = [NSNumber numberWithInt:statusCode];
        [self sendEventVtap:VTAP_SETUP_ACTION param:returnCode ];
    }
    
//    NSLog(@"^TID: %@", [vtapMgr getTroubleshootingId]);
//    NSLog(@"^VOS: %@", [VosWrapper getProcessorVersion]);
//    NSLog(@"^firmware: %@", [VosWrapper getFirmwareVersion]);
}

- (void) vSetupVtap {
    
    dispatch_async(dispatch_get_global_queue( DISPATCH_QUEUE_PRIORITY_HIGH, 0), ^(void){
        
        if (self->vtapMgr) {
            [self->vtapMgr setupVTap];
        } else {
            // 1. Get VTap Manager Instance
            self->vtapMgr = [VTapManager sharedInstance];
            self->vtapMgr.delegate = self;
            
            self->vguardMgr = [VGuardManager sharedVGuardManager];// test
            VGuardThreats* vGuardThreats = [VGuardThreats sharedModule];
            [vGuardThreats setDelegate:self];
            
            if(self->mTlaUrl != nil) {
                NSLog(@"^VosWrapper.setLoggerBaseUrl: %@", self->mTlaUrl);
                [VosWrapper setLoggerBaseUrl:self->mTlaUrl];
            }
            
            if(self->mTiUrl != nil) {
                NSLog(@"^setThreatIntelligenceServerURL %@", self->mTiUrl);
                [self->vtapMgr setThreatIntelligenceServerURL:self->mTiUrl];
            }
            if(self->vtapServer != nil) {
                [self->vtapMgr setHostName:self->provisioningServer vtapServer:self->vtapServer];
                [self->vtapMgr setPKIHostName:self->pkiServer];
                [self->vtapMgr setAllowAutoSendingTroubleshootingLog:YES];
                [self->vtapMgr setAllowSendStacktraceLog:YES];
                // Call the setupVTap API to setup V-OS Smart Token
                [self->vtapMgr setupVTap];
            }
        }
    });
}

RCT_EXPORT_METHOD(setHostName:(NSString *)provServer vtapServer:(NSString *)vtapServer) {
    tiServer = [provServer substringToIndex:(provServer.length - @"/provision".length)];
    NSLog(@"^tiServer %@", self->tiServer);
    provisioningServer = provServer;
    NSLog(@"^provisioningServer %@", self->provisioningServer);
    self->vtapServer = vtapServer;
    NSLog(@"^vtapServer %@", self->vtapServer);
    pkiServer = tiServer;
    NSLog(@"^pkiServer %@", self->pkiServer);
}

RCT_EXPORT_METHOD(setupVTapWithVKeyExist:(BOOL)isVKeyExisting) {
    [vtapMgr setupVTap:isVKeyExisting];
}

RCT_EXPORT_METHOD(setLoggerBaseUrl:(NSString *)tlaURL) {
    mTlaUrl = tlaURL;
    NSLog(@"VosWrapper.setLoggerBaseUrl: %@", tlaURL);
    [VosWrapper setLoggerBaseUrl:tlaURL];
}

RCT_EXPORT_METHOD(getDFPHash:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSString *dfpHash = @"";
        dfpHash = [vtapMgr getDFPHash];
        
        NSLog(@"^DFP Hash: %@", dfpHash);
        resolve(dfpHash); // return the hash to JS
    }
    @catch (NSException *exception) {
        reject(@"get_dfp_hash_error", @"Failed to get DFP Hash", nil);
    }
}

RCT_EXPORT_METHOD(isProvisioningDone:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        BOOL provisioningDone = [vtapMgr isProvisioningDone];
        
        resolve(@(provisioningDone)); // Return a BOOL wrapped in NSNumber
    }
    @catch (NSException *exception) {
        reject(@"is_provisioning_done_error", @"Failed to check provisioning status", nil);
    }
}

RCT_EXPORT_METHOD(getAllProvisionedTokens:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        // Example: assume you have an NSArray of tokens
        NSArray *tokens = [vtapMgr getAllProvisionedTokens];
        
        NSLog(@"^Provisioned Tokens: %@", tokens);
        
        if (tokens != nil) {
            resolve(tokens); // Send array back to JS
        } else {
            resolve(@[]); // If no tokens, return empty array
        }
    }
    @catch (NSException *exception) {
        reject(@"get_all_tokens_error", @"Failed to get provisioned tokens", nil);
    }
}

RCT_EXPORT_METHOD(getTokenType:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Getting token type for serial: %@", tokenSerial);
        
        NSInteger tokenType = [vtapMgr getTokenType:tokenSerial];
        
        resolve(@(tokenType)); // Wrap NSInteger to NSNumber
    }
    @catch (NSException *exception) {
        reject(@"get_token_type_exception", @"Failed to get token type", nil);
    }
}
RCT_EXPORT_METHOD(getTroubleshootingId:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSString* TID = [vtapMgr getTroubleshootingId];
        resolve(TID);
    } else {
        reject(@"getTroubleshootingId", ERROR_mess_vtap, nil);
    }
}

RCT_EXPORT_METHOD(sendTroubleshootingLogs:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        int result = [vtapMgr sendTroubleshootingLogs];
        
        NSLog(@"^Send Troubleshooting Logs");
        resolve(@(result));
    }
    @catch (NSException *exception) {
        reject(@"send_troubleshooting_logs_exception", @"Exception while sending troubleshooting logs", nil);
    }
}

RCT_EXPORT_METHOD(getTrustedTime:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        // Get trusted time as int
        int trustedTime = [vtapMgr getTrustedTime];
        
        NSLog(@"^Trusted Time (int): %d", trustedTime);
        
        resolve(@(trustedTime)); // Wrap int into NSNumber for JS
    }
    @catch (NSException *exception) {
        reject(@"get_trusted_time_exception", @"Failed to get trusted time", nil);
    }
}

RCT_EXPORT_METHOD(setOtpLength:(NSInteger)length
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Setting OTP Length to: %ld", (long)length);
        
        // Validate input: must be 6, 7, or 8
        if (length < 6 || length > 8) {
            reject(@"set_otp_length_invalid", @"OTP length must be 6, 7, or 8", nil);
            return;
        }
        
        int resultCode = [vtapMgr setOtpLength:(int)length];
        resolve(@(resultCode));
    }
    @catch (NSException *exception) {
        reject(@"set_otp_length_exception", @"Exception while setting OTP length", nil);
    }
}

RCT_EXPORT_METHOD(generateTOTP:(NSInteger)num
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Generating TOTP with num: %ld", (long)num);
        NSArray *result = [vtapMgr generateTOTP:(int)num];
        NSLog(@"^OTP: %@", result);
        if (result != nil) {
            resolve(result); // Return result array to JS
        } else {
            reject(@"generate_totp_failed", @"Failed to generate TOTP", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"generate_totp_exception", @"Exception while generating TOTP", nil);
    }
}

RCT_EXPORT_METHOD(generateCR:(NSString *)msg
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Generating CR with message: %@", msg);
        
        NSArray *result = [vtapMgr generateCR:msg];
        
        if (result != nil) {
            resolve(result); // Return array back to JS
        } else {
            reject(@"generate_cr_failed", @"Failed to generate CR", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"generate_cr_exception", @"Exception while generating CR", nil);
    }
}

RCT_EXPORT_METHOD(generateTxS:(NSString *)account
                  amount:(NSString *)amount
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Generating TxS with account: %@, amount: %@", account, amount);
        
        NSArray *result = [vtapMgr generateTxS:account amount:amount];
        
        if (result != nil) {
            resolve(result); // Return array back to JS
        } else {
            reject(@"generate_txs_failed", @"Failed to generate transaction signature", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"generate_txs_exception", @"Exception while generating transaction signature", nil);
    }
}

RCT_EXPORT_METHOD(getTokenSerial:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Getting Token Serial");
        
        NSString *tokenSerial = [vtapMgr getTokenSerial];
        
        if (tokenSerial != nil) {
            resolve(tokenSerial); // Return serial string to JS
        } else {
            reject(@"get_token_serial_error", @"Token serial is not available", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"get_token_serial_exception", @"Exception while getting token serial", nil);
    }
}

RCT_EXPORT_METHOD(validateCheckSum:(NSArray *)provisioningInfo
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        ts = provisioningInfo[0];
        apin = provisioningInfo[1];
        if (provisioningInfo == nil || provisioningInfo.count != 2) {
            reject(@"validate_checksum_invalid_input", @"Provisioning info must contain exactly 2 elements (ts and apin)", nil);
            return;
        }
        
        BOOL success = [vtapMgr validateChecksum:[NSArray arrayWithObjects:ts, apin, nil]];
        NSLog(@"^Validating checksum with provisioning info: %@", provisioningInfo);
        
        if (success) {
            resolve(@(YES)); // Return true if checksum validation passed
        } else {
            reject(@"validate_checksum_failed", @"Checksum validation failed", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"validate_checksum_exception", @"Exception during checksum validation", nil);
    }
}

RCT_EXPORT_METHOD(getLoadAckTokenFirmware:(NSArray *)provisioningInfo
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        // Validate input
        if (provisioningInfo == nil || provisioningInfo.count != 2) {
            reject(@"get_load_ack_token_firmware_invalid_input", @"Provisioning info must contain exactly 2 elements (ts and apin)", nil);
            return;
        }
        
        ts = provisioningInfo[0];
        apin = provisioningInfo[1];
        NSLog(@"^Getting Load Ack Token Firmware with provisioning info: %@", provisioningInfo);
        //        BOOL isValidateChecksumSucceded = [vtapMgr validateChecksum:[NSArray arrayWithObjects:ts, apin, nil]];
        //        if (!isValidateChecksumSucceded) {
        //            reject(@"validateChecksum", @"validateChecksum fail", nil);
        //            return;
        //        }
        
        
        
        NSData *decodedData;
        if ((apin.length == 88) || (apin.length == 108)) {
            //encrypted APIN need to base64 decode and hex
            if ([apin respondsToSelector:@selector(initWithBase64EncodedString:options:)]) {
                decodedData = [[NSData alloc] initWithBase64EncodedString:apin options:0];
            } else {
                decodedData = [NSData dataFromBase64String:apin];
            }
            apin = [[decodedData hexadecimalString] mutableCopy];
        }
        NSLog(@"^ts: %@, apin: %@", ts, apin);
        
        int getTokenFirmwareStatus = [vtapMgr getLoadAckTokenFirmware:[NSArray arrayWithObjects:ts, apin, nil]];
        if (getTokenFirmwareStatus == VTAP_TOKEN_DOWNLOAD_SUCCESS || getTokenFirmwareStatus == VTAP_LOAD_FIRMWARE_SUCCESS) {
            NSLog(@"^provision success");
        } else {
            NSLog(@"^provision fail: %d", getTokenFirmwareStatus);
        }
        
        NSLog(@"^Load Ack Token Firmware result: %d", getTokenFirmwareStatus);
        
        resolve(@(getTokenFirmwareStatus)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"get_load_ack_token_firmware_exception", @"Exception during load ack token firmware", nil);
    }
}

RCT_EXPORT_METHOD(getLoadAckTokenFirmwareWithHttpPost:(NSArray *)provisioningInfo
                  isHttpPost:(BOOL)isHttpPost
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        // Validate input
        if (provisioningInfo == nil || provisioningInfo.count != 2) {
            reject(@"get_load_ack_token_firmware_invalid_input", @"Provisioning info must contain exactly 2 elements (ts and apin)", nil);
            return;
        }
        
        ts = provisioningInfo[0];
        apin = provisioningInfo[1];
        NSLog(@"^Getting Load Ack Token Firmware with provisioning info: %@", provisioningInfo);
        //        BOOL isValidateChecksumSucceded = [vtapMgr validateChecksum:[NSArray arrayWithObjects:ts, apin, nil]];
        //        if (!isValidateChecksumSucceded) {
        //            reject(@"validateChecksum", @"validateChecksum fail", nil);
        //            return;
        //        }
        
        
        
        NSData *decodedData;
        if ((apin.length == 88) || (apin.length == 108)) {
            //encrypted APIN need to base64 decode and hex
            if ([apin respondsToSelector:@selector(initWithBase64EncodedString:options:)]) {
                decodedData = [[NSData alloc] initWithBase64EncodedString:apin options:0];
            } else {
                decodedData = [NSData dataFromBase64String:apin];
            }
            apin = [[decodedData hexadecimalString] mutableCopy];
        }
        NSLog(@"^ts: %@, apin: %@", ts, apin);
        int getTokenFirmwareStatus = [vtapMgr getLoadAckTokenFirmware:[NSArray arrayWithObjects:ts, apin, nil] httpPostMethod:isHttpPost];
        if (getTokenFirmwareStatus == VTAP_TOKEN_DOWNLOAD_SUCCESS || getTokenFirmwareStatus == VTAP_LOAD_FIRMWARE_SUCCESS) {
            NSLog(@"^provision success");
        } else {
            NSLog(@"^provision fail: %d", getTokenFirmwareStatus);
        }
        
        NSLog(@"^Load Ack Token Firmware result: %d", getTokenFirmwareStatus);
        
        resolve(@(getTokenFirmwareStatus)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"get_load_ack_token_firmware_http_exception", @"Exception during load ack token firmware (HTTP POST)", nil);
    }
}

RCT_EXPORT_METHOD(loadTokenFirmware:(NSString *)tokenSerial
                  aPin:(NSString *)aPin
                  downloadFilePath:(NSString *)downloadFilePath
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Loading token firmware with tokenSerial: %@, aPin: %@, downloadFilePath: %@", tokenSerial, aPin, downloadFilePath);
        int resultCode = [vtapMgr loadTokenFirmware:tokenSerial APIN:aPin downloadFilePath:downloadFilePath];
        
        NSLog(@"^Load Token Firmware result code: %d", resultCode);
        
        resolve(@(resultCode)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"load_token_firmware_exception", @"Exception during loading token firmware", nil);
    }
}

RCT_EXPORT_METHOD(removeTokenFirmware:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Removing token firmware for tokenSerial: %@", tokenSerial);
        
        int resultCode = [vtapMgr removeTokenFirmware:tokenSerial];
        
        NSLog(@"^Remove Token Firmware result code: %d", resultCode);
        
        resolve(@(resultCode)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"remove_token_firmware_exception", @"Exception during removing token firmware", nil);
    }
}

RCT_EXPORT_METHOD(loadToken:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Loading token with tokenSerial: %@", tokenSerial);
        
        int resultCode = [vtapMgr loadToken:tokenSerial];
        
        NSLog(@"^Load Token result code: %d", resultCode);
        
        resolve(@(resultCode)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"load_token_exception", @"Exception during loading token", nil);
    }
}

RCT_EXPORT_METHOD(unloadToken:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Unloading token with tokenSerial: %@", tokenSerial);
        
        int resultCode = [vtapMgr unloadToken:tokenSerial];
        
        NSLog(@"^Unload Token result code: %d", resultCode);
        
        resolve(@(resultCode)); // Return the int result code
    }
    @catch (NSException *exception) {
        reject(@"unload_token_exception", @"Exception during unloading token", nil);
    }
}

RCT_EXPORT_METHOD(getTokenFirmwareVersion:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Getting token firmware version for tokenSerial: %@", tokenSerial);
        
        NSString *firmwareVersion = [vtapMgr getTokenFirmwareVersion:tokenSerial];
        
        NSLog(@"^Firmware Version: %@", firmwareVersion);
        
        if (firmwareVersion != nil) {
            resolve(firmwareVersion); // Return firmware version string to JS
        } else {
            reject(@"get_token_firmware_version_error", @"Firmware version is not available", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"get_token_firmware_version_exception", @"Exception during getting token firmware version", nil);
    }
}

RCT_EXPORT_METHOD(isTokenRegistered:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        BOOL isRegistered = false;
        
        isRegistered = [vtapMgr isTokenRegistered:tokenSerial];
        
        NSLog(@"^Is Token Registered: %@", isRegistered ? @"YES" : @"NO");
        
        resolve(@(isRegistered)); // Return BOOL wrapped as NSNumber
    }
    @catch (NSException *exception) {
        reject(@"is_token_registered_exception", @"Exception during checking token registration", nil);
    }
}

RCT_EXPORT_METHOD(createTokenPIN:(NSString *)pin
                  tokenSerial:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Creating Token PIN for tokenSerial: %@ with PIN: %@", tokenSerial, pin);
        
        int resultCode = [vtapMgr createTokenPIN:pin withTokenSerial:tokenSerial];
        
        NSLog(@"^Create Token PIN result code: %d", resultCode);
        
        resolve(@(resultCode)); // Return int result code
    }
    @catch (NSException *exception) {
        reject(@"create_token_pin_exception", @"Exception during creating token PIN", nil);
    }
}

//  Multiple TA: Token PIN Management API

RCT_EXPORT_METHOD(checkTokenPIN:(NSString *)pin
                  rememberPin:(BOOL)rememberPin
                  tokenSerial:(NSString *)tokenSerial
                  seedNumber:(NSInteger)seedNumber
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        NSLog(@"^Checking Token PIN for tokenSerial: %@, pin: %@, rememberPin: %@, seedNumber: %ld",
              tokenSerial, pin, rememberPin ? @"YES" : @"NO", (long)seedNumber);
        int result = ERROR;
        if (vtapMgr != nil) {
            if (seedNumber == 0) {
                result = [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial];
            } else {
                result = [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial andSeed:(int)seedNumber];
            }
            NSLog(@"^Check Token PIN result code: %d", result);
            
            resolve([NSNumber numberWithInt:result]);
        } else {
            reject(@"check_token_pin_fail", ERROR_mess_vtap, nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"check_token_pin_exception", @"Exception during checking token PIN", nil);
    }
}

RCT_EXPORT_METHOD(isTokenPINRemembered:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        
        BOOL isRemembered = false;
        isRemembered = [vtapMgr isTokenPINRemembered:tokenSerial];
        
        NSLog(@"^Is Token PIN Remembered: %@", isRemembered ? @"YES" : @"NO");
        
        resolve(@(isRemembered)); // Return BOOL wrapped as NSNumber
    }
    @catch (NSException *exception) {
        reject(@"is_token_pin_remembered_exception", @"Exception during checking if Token PIN is remembered", nil);
    }
}

RCT_EXPORT_METHOD(changeTokenPIN:(NSString *)oldPIN
                  newPIN:(NSString *)newPIN
                  tokenSerial:(NSString *)tokenSerial
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    @try {
        if (vtapMgr != nil) {
            NSLog(@"^Changing Token PIN for tokenSerial: %@, oldPIN: %@, newPIN: %@", tokenSerial, oldPIN, newPIN);
            
            int resultCode = [vtapMgr changeTokenPIN:oldPIN withNewPIN:newPIN withTokenSerial:tokenSerial];
            
            NSLog(@"^Change Token PIN result code: %d", resultCode);
            
            resolve(@(resultCode));
        } else {
            reject(@"vtapMgr_null", @"VTap Manager is not initialized.", nil);
        }
    }
    @catch (NSException *exception) {
        reject(@"change_token_pin_exception", exception.reason ?: @"Unknown exception during changing Token PIN", nil);
    }
}
//RCT_EXPORT_METHOD(setMemoryConfiguration:(nonnull NSNumber *)memConfig) {
//    VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
//    [vgManager setMemoryConfiguration:[memConfig integerValue]];
//}

RCT_EXPORT_METHOD(setMemoryConfiguration:(NSInteger)config) {
    @try {
        NSLog(@"^Setting Memory Configuration to: %ld", (long)config);
        VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
        [vgManager setMemoryConfiguration:(int)config];
    }
    @catch (NSException *exception) {
        NSLog(@"^Exception while setting memory configuration: %@", exception.reason);
    }
}

RCT_EXPORT_METHOD(setDebuggable:(BOOL)debuggable) {
    @try {
        NSLog(@"^Setting Debuggable to: %@", debuggable ? @"YES" : @"NO");
        vguardMgr = [VGuardManager sharedVGuardManager];
        [vguardMgr setIsDebug:debuggable];
        
    }
    @catch (NSException *exception) {
        NSLog(@"^Exception while setting debuggable: %@", exception.reason);
    }
}

RCT_EXPORT_METHOD(setAllowsArbitraryNetworking:(BOOL)enable) {
    @try {
        self->sslPinning = &enable;
        if (vguardMgr != nil) {
            [vguardMgr allowsArbitraryNetworking:enable];
        }
    }
    @catch (NSException *exception) {
        NSLog(@"^Exception while setting arbitrary networking: %@", exception.reason);
    }
}

RCT_EXPORT_METHOD(setThreatIntelligenceServerURL:(NSString *)tiURL) {
    mTiUrl = tiURL;
    if (vguardMgr != nil) {
        [vguardMgr setThreatIntelligenceServerURL:tiURL];
    }
}

// Generic : Lifecycle Actions API
RCT_EXPORT_METHOD(onResume) {
    if (vguardMgr != nil) {
        [vtapMgr onResume];
    }
}

RCT_EXPORT_METHOD(onPause) {
    if (vguardMgr != nil) {
        [vtapMgr onPause];
    }
}

RCT_EXPORT_METHOD(onDestroy) {
    if (vguardMgr != nil) {
        [vtapMgr onDestroy];
    }
}

RCT_EXPORT_METHOD(setupVtap) {
    [self vSetupVtap];
}

RCT_EXPORT_METHOD(getIsVosStarted:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        bool result = [vguardMgr initializeVGuard];
        resolve([NSNumber numberWithBool: result]);
    } else {
        reject(@"getIsVosStarted", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(resetVOSTrustedStorage) {
    if (vguardMgr != nil) {
        [vguardMgr resetVOSTrustedStorage];
    }
}

RCT_EXPORT_METHOD(destroy) {
    if (vguardMgr != nil) {
        if ([vguardMgr respondsToSelector:@selector(destroy)]) {
            [vguardMgr destroy];
        } else {
            NSLog(@"destroy: API not supported in iOS");
        }
    }
}

//PKI TA -  Registration

RCT_EXPORT_METHOD(setPKIHostName:(NSString *)pkiServer) {
    [vtapMgr setPKIHostName: pkiServer];
}

RCT_EXPORT_METHOD(getPKITokenSerial:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString* ts = nil;
    if (vtapMgr != nil) {
        ts = [vtapMgr getPKITokenSerial];
        resolve(ts);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

RCT_EXPORT_METHOD(requestScan) {
    // Android: - (void)requestScan;
    if (vguardMgr != nil) {
        return [vguardMgr start];
    }
}

RCT_EXPORT_METHOD(getCustomerID:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* cusId = [vguardMgr getCustomerID];
        resolve(cusId);
    } else {
        reject(@"getCustomerID", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getProcessorVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* processorVer = [VosWrapper getProcessorVersion];
        resolve(processorVer);
    } else {
        reject(@"getProcessorVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getFirmwareVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* firmwareVer = [VosWrapper getFirmwareVersion];
        resolve(firmwareVer);
    } else {
        reject(@"getFirmwareVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(sdkVersion:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* cusId = [vguardMgr sdkVersion];
        resolve(cusId);
    } else {
        reject(@"sdkVersion", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getPassword:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* pass = [vguardMgr getPassword];
        resolve(pass);
    } else {
        reject(@"getPassword", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(lockVos:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        int result = [vguardMgr lockVOS];
        resolve([NSNumber numberWithInt:result]);
    } else {
        reject(@"lockVOS", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(encryptUsingCustomerKey:(NSString *)string resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* encryptStr = [vguardMgr encryptString:string];
        resolve(encryptStr);
    } else {
        reject(@"encryptString", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(decryptUsingCustomerKey:(NSString *)string resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* decryptStr = [vguardMgr decryptString:string];
        resolve(decryptStr);
    } else {
        reject(@"decryptString", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getDFPHashHash:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* dfp = [vguardMgr getDFPHashHash];
        resolve(dfp);
    } else {
        reject(@"getDFPHashHash", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(getVGuardVersionInformation:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vguardMgr != nil) {
        NSString* version = [vguardMgr getVGuardVersionInformation];
        resolve(version);
    } else {
        reject(@"getVGuardVersionInformation", ERROR_mess, nil);
    }
}

RCT_EXPORT_METHOD(setDebugable:(BOOL)debug) {
    if (vguardMgr != nil) {
        [vguardMgr setIsDebug:debug];
    }
}

RCT_EXPORT_METHOD(setMaximumNetworkRetryTime:(nonnull NSNumber *)memConfig) {
    VGuardManager *vgManager = [VGuardManager sharedVGuardManager];
    [vgManager setMaximumNetworkRetryTime:[memConfig intValue]];
}

RCT_EXPORT_METHOD(forceSyncLogs) {
    NSLog(@"VosWrapper.forceSyncLogs");
    int execRet = [VosWrapper execute:nil];
    if (execRet > 0) {
        [VosWrapper forceSyncLogs];
    }
}

@end
