//
//  ResultCodeModule.m
//  RNVosSmarttokenReactNative
//
//  Created by <PERSON><PERSON><PERSON> (An) on 5/21/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

#import "ResultCodeModule.h"

@implementation ResultCodeModule

RCT_EXPORT_MODULE(ResultCode);

- (NSDictionary *)constantsToExport
{
    return @{ @"VTAP_SETUP_SUCCESS": @VTAP_SETUP_SUCCESS,
              @"VGUARD_FAILED": @VGUARD_FAILED,
              @"VOS_FAILED": @VOS_FAILED,
              @"VTAP_TOKEN_LOAD_FAILED": @VTAP_TOKEN_LOAD_FAILED,
              @"VTAP_SETUP_FAILED": @VTAP_SETUP_FAILED,
              
              // CHECK DEVICE COMPATIBILITY
              @"VTAP_WHITE_LISTED_DEVICE": @VTAP_WHITE_LISTED_DEVICE,
              @"VTAP_BLACK_LISTED_DEVICE": @VTAP_BLACK_LISTED_DEVICE,
              @"VTAP_GREY_LISTED_DEVICE": @VTAP_GREY_LISTED_DEVICE,
              @"VTAP_GET_DEVICE_LIST_FAILED": @VTAP_GET_DEVICE_LIST_FAILED,

              // SEND DEVICE INFO
              @"VTAP_SEND_DEVICE_INFO_SUCCESS": @VTAP_SEND_DEVICE_INFO_SUCCESS,
              @"VTAP_SEND_DEVICE_INFO_FAILED": @VTAP_SEND_DEVICE_INFO_FAILED,

              // SEND ERROR LOGS
              @"VTAP_SEND_TROUBLESHOOTING_LOGS_SUCCESS": @VTAP_SEND_TROUBLESHOOTING_LOGS_SUCCESS,
              @"VTAP_SEND_TROUBLESHOOTING_LOGS_FAILED": @VTAP_SEND_TROUBLESHOOTING_LOGS_FAILED,

              @"VTAP_TOKEN_DOWNLOAD_SUCCESS": @VTAP_TOKEN_DOWNLOAD_SUCCESS,
              @"VTAP_ERROR_INVALID_PROVISIONING_INFO": @VTAP_ERROR_INVALID_PROVISIONING_INFO,
              @"VTAP_ERROR_INVALID_TOKEN_SERIAL": @VTAP_ERROR_INVALID_TOKEN_SERIAL,
              @"VTAP_ERROR_INVALID_ACTIVATION_PIN": @VTAP_ERROR_INVALID_ACTIVATION_PIN,
              @"VTAP_TOKEN_DOWNLOAD_FAILED": @VTAP_TOKEN_DOWNLOAD_FAILED,
              @"VTAP_TOKEN_NOT_FOUND": @VTAP_TOKEN_NOT_FOUND,

              @"VTAP_TOKEN_BAD_REQUEST": @VTAP_TOKEN_BAD_REQUEST,
              @"VTAP_TOKEN_INVALID_DFP": @VTAP_TOKEN_INVALID_DFP,
              @"VTAP_LOAD_FIRMWARE_SUCCESS": @VTAP_LOAD_FIRMWARE_SUCCESS,
              @"VTAP_TOKEN_UNZIP_FAILED": @VTAP_TOKEN_UNZIP_FAILED,
              @"VTAP_TOKEN_PROCESSING_FAILED": @VTAP_TOKEN_PROCESSING_FAILED,
              @"VTAP_LOAD_FIRMWARE_FAILED": @VTAP_LOAD_FIRMWARE_FAILED,
              @"VTAP_LOAD_TOKEN_SUCCESS": @VTAP_LOAD_TOKEN_SUCCESS,
              @"VTAP_LOAD_TOKEN_FAILED": @VTAP_LOAD_TOKEN_FAILED,
              @"VTAP_UNLOAD_TOKEN_SUCCESS": @VTAP_UNLOAD_TOKEN_SUCCESS,
              @"VTAP_UNLOAD_TOKEN_FAILED": @VTAP_UNLOAD_TOKEN_FAILED,
              @"VTAP_SET_DEFAULT_TOKEN_SUCCESS": @VTAP_SET_DEFAULT_TOKEN_SUCCESS,
              @"VTAP_TOKEN_NOT_PROVISIONED": @VTAP_TOKEN_NOT_PROVISIONED,
              @"VTAP_REMOVE_TOKEN_SUCCESS": @VTAP_REMOVE_TOKEN_SUCCESS,
              @"VTAP_REMOVE_TOKEN_FAILED": @VTAP_REMOVE_TOKEN_FAILED,

              // USER PIN REGISTRATION
              @"VTAP_CREATE_PIN_SUCCESS": @VTAP_CREATE_PIN_SUCCESS,
              @"VTAP_CREATE_PIN_FAILED": @VTAP_CREATE_PIN_FAILED,
              @"VTAP_PIN_ALREADY_EXIST": @VTAP_PIN_ALREADY_EXIST,
              @"VTAP_CHECK_PIN_SUCCESS": @VTAP_CHECK_PIN_SUCCESS,
              @"VTAP_CHECK_PIN_FAILED": @VTAP_CHECK_PIN_FAILED,
              @"VTAP_VOS_LOCKED": @VTAP_VOS_LOCKED,
              @"VTAP_CHANGE_PIN_SUCCESS": @VTAP_CHANGE_PIN_SUCCESS,
              @"VTAP_CHANGE_PIN_SUCCESS": @VTAP_CHANGE_PIN_SUCCESS,
              @"VTAP_CHANGE_PIN_FAILED": @VTAP_CHANGE_PIN_FAILED,
              @"VTAP_CHANGE_INCORRECT_PIN_FAILED": @VTAP_CHANGE_INCORRECT_PIN_FAILED,
              
              // TRANSACTION
              @"VTAP_SET_OTP_LENGTH_SUCCESS": @VTAP_SET_OTP_LENGTH_SUCCESS,
              @"VTAP_SET_OTP_LENGTH_FAILED": @VTAP_SET_OTP_LENGTH_FAILED,
              @"VTAP_GENERATE_TOTP_FAILED": @VTAP_GENERATE_TOTP_FAILED,
              @"VTAP_TOTP_UNSUPPORTED_KEY_OPTION": @VTAP_TOTP_UNSUPPORTED_KEY_OPTION,
              @"VTAP_GENERATE_CR_FAILED": @VTAP_GENERATE_CR_FAILED,
              @"VTAP_CR_UNSUPPORTED_MSG_LENGTH": @VTAP_CR_UNSUPPORTED_MSG_LENGTH,
              @"VTAP_GENERATE_TXS_FAILED": @VTAP_GENERATE_TXS_FAILED,
              @"VTAP_TXS_UNSUPPORTED_MSG_LENGTH": @VTAP_TXS_UNSUPPORTED_MSG_LENGTH,
              @"VTAP_UNSUPPORTED_OTP_LENGTH": @VTAP_UNSUPPORTED_OTP_LENGTH,
              @"VTAP_INCORRECT_API_CALL_SEQUENCE": @VTAP_INCORRECT_API_CALL_SEQUENCE,
              @"VTAP_TIME_FAILURE": @VTAP_TIME_FAILURE,

              // OTHER RESULTS
              @"VTAP_INVALID_INPUT": @VTAP_INVALID_INPUT,
              @"VTAP_ERROR_CONNECTION_FAILED": @VTAP_ERROR_CONNECTION_FAILED,
              @"VTAP_INVALID_API_SEQUENCE": @VTAP_INVALID_API_SEQUENCE,

              // PKI
              @"VTAP_PUSH_NOTIFICATION_REGISTRATION_SUCCESS": @VTAP_PUSH_NOTIFICATION_REGISTRATION_SUCCESS,
              @"VTAP_PUSH_NOTIFICATION_REGISTRATION_FAILED": @VTAP_PUSH_NOTIFICATION_REGISTRATION_FAILED,
              
              // Trigger push notification
              @"VTAP_PKI_TRIGGER_SEND_CERT_SUCCESS": @VTAP_PKI_TRIGGER_SEND_CERT_SUCCESS,
              @"VTAP_PKI_TRIGGER_SEND_CERT_FAILED": @VTAP_PKI_TRIGGER_SEND_CERT_FAILED,

              @"VTAP_PKI_SEND_CERT_ACTIVATION_SUCCESS": @VTAP_PKI_SEND_CERT_ACTIVATION_SUCCESS,
              @"VTAP_PKI_SEND_CERT_ACTIVATION_FAILED": @VTAP_PKI_SEND_CERT_ACTIVATION_FAILED,
              
              @"VTAP_PKI_CSR_REGISTRATION_SUCCESS": @VTAP_PKI_CSR_REGISTRATION_SUCCESS,
              @"VTAP_PKI_CSR_REGISTRATION_FAILED": @VTAP_PKI_CSR_REGISTRATION_FAILED,
              
              @"VTAP_PKI_AUTHENTICATION_SUCCESS": @VTAP_PKI_AUTHENTICATION_SUCCESS,
              @"VTAP_PKI_AUTHENTICATION_FAILED": @VTAP_PKI_AUTHENTICATION_FAILED,
              @"VTAP_PKI_AUTHENTICATION_CERT_NOT_AVAILABLE": @VTAP_PKI_AUTHENTICATION_CERT_NOT_AVAILABLE,
              
              @"VTAP_PKI_DOC_SIGNING_SUCCESS": @VTAP_PKI_DOC_SIGNING_SUCCESS,
              @"VTAP_PKI_DOC_SIGNING_FAILED": @VTAP_PKI_DOC_SIGNING_FAILED,
              @"VTAP_PKI_DOC_SIGNING_CERT_NOT_AVAILABLE": @VTAP_PKI_DOC_SIGNING_CERT_NOT_AVAILABLE,
              
              @"VTAP_PKI_MESSAGE_DOWNLOAD_FAILED": @VTAP_PKI_MESSAGE_DOWNLOAD_FAILED,
              
              @"VTAP_PKI_CERT_DOWNLOAD_SUCCESS": @VTAP_PKI_CERT_DOWNLOAD_SUCCESS,
              @"VTAP_PKI_CERT_DOWNLOAD_FAILED": @VTAP_PKI_CERT_DOWNLOAD_FAILED,

              @"VTAP_PKI_CHECK_PIN_SUCCESS": @VTAP_PKI_CHECK_PIN_SUCCESS,
              @"VTAP_PKI_CHECK_PIN_FAILED": @VTAP_PKI_CHECK_PIN_FAILED,

              @"VTAP_PKI_CHANGE_PIN_SUCCESS": @VTAP_PKI_CHANGE_PIN_SUCCESS,
              @"VTAP_PKI_CHANGE_PIN_FAILED": @VTAP_PKI_CHANGE_PIN_FAILED,
              @"VTAP_PKI_CHANGE_INCORRECT_PIN_FAILED": @VTAP_PKI_CHANGE_INCORRECT_PIN_FAILED,
              
              @"VTAP_PKI_CERT_VERIFICATION_FAILED": @VTAP_PKI_CERT_VERIFICATION_FAILED,

              @"VTAP_VMESSAGE_DECRYPT_MSG_FAILED": @VTAP_VMESSAGE_DECRYPT_MSG_FAILED,

              @"VTAP_VMESSAGE_SEND_ACK_SUCCESS": @VTAP_VMESSAGE_SEND_ACK_SUCCESS,
              @"VTAP_VMESSAGE_SEND_ACK_FAILED": @VTAP_VMESSAGE_SEND_ACK_FAILED,
              @"VTAP_PKI_VMESSAGE_CERT_NOT_AVAILABLE": @VTAP_PKI_VMESSAGE_CERT_NOT_AVAILABLE,

              @"VTAP_PKI_AUTHENTICATION_REJECTION_SUCCESS": @VTAP_PKI_AUTHENTICATION_REJECTION_SUCCESS,
              @"VTAP_PKI_AUTHENTICATION_REJECTION_FAILED": @VTAP_PKI_AUTHENTICATION_REJECTION_FAILED,

              // New Rest APIs
              @"VTAP_CREATE_ADDITIONAL_DATA_SUCCESS": @VTAP_CREATE_ADDITIONAL_DATA_SUCCESS,
              @"VTAP_INVALID_FORMAT": @VTAP_INVALID_FORMAT,
              @"VTAP_DISABLE_ADDITIONAL_DATA": @VTAP_DISABLE_ADDITIONAL_DATA,
              @"VTAP_CREATE_ADDITIONAL_MESSAGE_TYPE_SUCCESS": @VTAP_CREATE_ADDITIONAL_MESSAGE_TYPE_SUCCESS,
              
              // Netrust
              @"VTAP_GENERATE_ECC_CSR_WITH_ALIAS_SUCCEEDED": @VTAP_GENERATE_ECC_CSR_WITH_ALIAS_SUCCEEDED,
              @"VTAP_GENERATE_ECC_CSR_WITH_ALIAS_FAILED": @VTAP_GENERATE_ECC_CSR_WITH_ALIAS_FAILED,
              @"VTAP_DELETE_ECC_KEY_WITH_ALIAS_SUCCEEDED": @VTAP_DELETE_ECC_KEY_WITH_ALIAS_SUCCEEDED,
              @"VTAP_DELETE_ECC_KEY_WITH_ALIAS_FAILED": @VTAP_DELETE_ECC_KEY_WITH_ALIAS_FAILED,
              @"VTAP_ECC_SIGN_WITH_ALIAS_SUCCEEDED": @VTAP_ECC_SIGN_WITH_ALIAS_SUCCEEDED,
              @"VTAP_ECC_SIGN_WITH_ALIAS_FAILED": @VTAP_ECC_SIGN_WITH_ALIAS_FAILED,
              @"VTAP_ECC_VERIFY_WITH_ALIAS_SUCCEEDED": @VTAP_ECC_VERIFY_WITH_ALIAS_SUCCEEDED,
              @"VTAP_ECC_VERIFY_WITH_ALIAS_FAILED": @VTAP_ECC_VERIFY_WITH_ALIAS_FAILED,
              @"VTAP_ADD_CERT_WITH_ALIAS_SUCCEEDED": @VTAP_ADD_CERT_WITH_ALIAS_SUCCEEDED,
              @"VTAP_ADD_CERT_WITH_ALIAS_FAILED": @VTAP_ADD_CERT_WITH_ALIAS_FAILED,
              @"VTAP_GET_CERT_WITH_ALIAS_SUCCEEDED": @VTAP_GET_CERT_WITH_ALIAS_SUCCEEDED,
              @"VTAP_GET_CERT_WITH_ALIAS_FAILED": @VTAP_GET_CERT_WITH_ALIAS_FAILED,
              @"VTAP_DELETE_CERT_WITH_ALIAS_SUCCEEDED": @VTAP_DELETE_CERT_WITH_ALIAS_SUCCEEDED,
              @"VTAP_DELETE_CERT_WITH_ALIAS_FAILED": @VTAP_DELETE_CERT_WITH_ALIAS_FAILED,
              @"VTAP_GET_KEY_ALIASES_SUCCEEDED": @VTAP_GET_KEY_ALIASES_SUCCEEDED,
              @"VTAP_GET_KEY_ALIASES_FAILED": @VTAP_GET_KEY_ALIASES_FAILED,
              @"VTAP_GET_CERT_ALIASES_SUCCEEDED": @VTAP_GET_CERT_ALIASES_SUCCEEDED,
              @"VTAP_GET_CERT_ALIASES_FAILED": @VTAP_GET_CERT_ALIASES_FAILED,
              @"VTAP_IS_KEY_ALIAS_FAILED": @VTAP_IS_KEY_ALIAS_FAILED,
              @"VTAP_IS_CERT_ALIAS_FAILED": @VTAP_IS_CERT_ALIAS_FAILED,
              @"VTAP_RESET_TOKEN_FIRMWARE_SUCCEEDED": @VTAP_RESET_TOKEN_FIRMWARE_SUCCEEDED,
              @"VTAP_RESET_TOKEN_FIRMWARE_FAILED": @VTAP_RESET_TOKEN_FIRMWARE_FAILED,
              @"VTAP_ALIAS_FOUND": @VTAP_ALIAS_FOUND,
              @"VTAP_ALIAS_NOT_FOUND": @VTAP_ALIAS_NOT_FOUND,
              @"VTAP_PKI_VMESSAGE_DECRYPTION_FAILED": @41125
            };
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;  // only do this if your module initialization relies on calling UIKit!
}

@end


