//
//  VKSecureKeypad.m
//  RNVosSmarttokenReactNative
//
//  Created by <PERSON><PERSON><PERSON> (An) on 5/23/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

#import "VKSecureKeypad.h"
#import <React/RCTLog.h>

@interface VKSecureKeypad (){
}

@end

@implementation VKSecureKeypad

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(setEnableScrambleKeypad:(BOOL)enable) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setEnableScrambleKeypad:enable];
}

RCT_EXPORT_METHOD(setKeypadTextColor:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadTextColor: [self colorFromHexString:color]];
}

RCT_EXPORT_METHOD(setKeypadButtonAlpha:(CGFloat)alpha) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadButtonAlpha:alpha];
}

RCT_EXPORT_METHOD(setEnableKeyboard:(BOOL)enable) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setEnableKeyboard:enable];
}

RCT_EXPORT_METHOD(keyTouchHighlightColor:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyTouchHighlightColor: [self colorFromHexString:color]];

}

//not yet
RCT_EXPORT_METHOD(keypadButtonImage:(UIImage *)image) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadButtonImage: image];
}

RCT_EXPORT_METHOD(setKeypadButtonBackgroundColor:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadButtonBackgroundColor: [self colorFromHexString:color]];
}

//not yet
RCT_EXPORT_METHOD(setKeypadDeleteButtonFont:(UIFont *)font) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadDeleteButtonFont:font];
}

RCT_EXPORT_METHOD(setKeypadDeleteButtonText:(NSString *)text) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadDeleteButtonText:text];
}

//not yet
RCT_EXPORT_METHOD(setKeypadCloseButtonFont:(UIFont *)font) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadCloseButtonFont:font];
}

RCT_EXPORT_METHOD(setKeypadCloseButtonText:(NSString *)text) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeypadCloseButtonText:text];
}

RCT_EXPORT_METHOD(setKeyboardTextColor:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardTextColor:[self colorFromHexString:color]];
}

RCT_EXPORT_METHOD(setKeyboardDeleteButtonText:(NSString *)text) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardDeleteButtonText:text];
}

RCT_EXPORT_METHOD(setKeyboardCloseButtonText:(NSString *)text) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardCloseButtonText:text];
}

RCT_EXPORT_METHOD(setKeyboardShiftButtonText:(NSString *)text) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardShiftButtonText:text];
}

//not yet
RCT_EXPORT_METHOD(setKeyboardButtonImage:(UIImage *)img) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardButtonImage:img];
}

RCT_EXPORT_METHOD(setKeyboardButtonBackgroundColor:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardButtonBackgroundColor:[self colorFromHexString:color]];
}

RCT_EXPORT_METHOD(setKeyboardButtonAlpha:(CGFloat)alpha) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardButtonAlpha:alpha];
}

RCT_EXPORT_METHOD(setKeyboardBackgroundColour:(NSString *)color) {
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setKeyboardBackgroundColour:[self colorFromHexString:color]];
}

- (UIColor *)colorFromHexString:(NSString *)hexString {
    unsigned rgbValue = 0;
    NSScanner *scanner = [NSScanner scannerWithString:hexString];
    [scanner setScanLocation:1]; // bypass '#' character
    [scanner scanHexInt:&rgbValue];
    return [UIColor colorWithRed:((rgbValue & 0xFF0000) >> 16)/255.0 green:((rgbValue & 0xFF00) >> 8)/255.0 blue:(rgbValue & 0xFF)/255.0 alpha:1.0];
}

@end
