<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/VGuard.framework/Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGuard-Swift.h</key>
		<data>
		OYp+k432cGOPdFc7rWW0Gz+maSM=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGuardManager.h</key>
		<data>
		H0coipAoUJoXGsPDSioqJIcIQfY=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VGuardThreats.h</key>
		<data>
		CDZGRDZuMS78xqyMrkdwitrnm8M=
		</data>
		<key>ios-arm64/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>ios-arm64/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<data>
		51zmKixeJCA2EXyytbh+qBlU+7g=
		</data>
		<key>ios-arm64/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<data>
		SE8x/OoInHPM0Y2ZPXsNzYiSY84=
		</data>
		<key>ios-arm64/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>ios-arm64/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>ios-arm64/VGuard.framework/Info.plist</key>
		<data>
		WS7+91RG1ccn8iEGH0WyzMkj3Gw=
		</data>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
		</data>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		deygybyRFZyPLessWeVWafBzlRA=
		</data>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
		</data>
		<key>ios-arm64/VGuard.framework/Modules/module.modulemap</key>
		<data>
		E4/CbwB3LVIhIc0LUz7RYzd8As8=
		</data>
		<key>ios-arm64/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>ios-arm64/VGuard.framework/VGuard</key>
		<data>
		ZCkjmwR8oQXmZF5kbPQXYWjlHqU=
		</data>
		<key>ios-arm64/VGuard.framework/VGuard_Info.plist</key>
		<data>
		bPvAvMmqCHi0b8w3G0Qdp3CTpX0=
		</data>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<data>
		2l9s6dAcnxvuIUkq/UjAr72SwXM=
		</data>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<data>
		SWUMFGibgWcSsLKuf0ecY/l8nNE=
		</data>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		lk/ksZHU9fWtTAvidnJ0XVOuoOc=
		</data>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeResources</key>
		<data>
		aI/eMN7VtC/OR2z7SARH6aRtcU8=
		</data>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeSignature</key>
		<data>
		GC4R//86Y61yZ1aC/jBlOREwiYo=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuard-Swift.h</key>
		<data>
		ospQqtertB8x3u4IoFLO3MibNHE=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardManager.h</key>
		<data>
		H0coipAoUJoXGsPDSioqJIcIQfY=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardThreats.h</key>
		<data>
		CDZGRDZuMS78xqyMrkdwitrnm8M=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<data>
		51zmKixeJCA2EXyytbh+qBlU+7g=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<data>
		SE8x/OoInHPM0Y2ZPXsNzYiSY84=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Info.plist</key>
		<data>
		5piOf7bBwpnJVe4Hi9bM7mzA9qE=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		tfN11YPD+r75K/S3FnU6w34JtiM=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zP4/zr3D09PKspKAJdzgdpiIVuk=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		tfN11YPD+r75K/S3FnU6w34JtiM=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		pl12kK57xnpbgdM8/Dfygk3fiCk=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		EFmglgeL94Qrj9qEaKHsmqUb1Jw=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		pl12kK57xnpbgdM8/Dfygk3fiCk=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/module.modulemap</key>
		<data>
		E4/CbwB3LVIhIc0LUz7RYzd8As8=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/VGuard</key>
		<data>
		Cj99QynB9Y1zlb4WM4XBkQOERY8=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/VGuard_Info.plist</key>
		<data>
		bPvAvMmqCHi0b8w3G0Qdp3CTpX0=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<data>
		A8ml5TEU8ZPzT/lXkm7pPgDh7L0=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<data>
		OnX22wWFKRSOFN1+obRynMCeyXM=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<data>
		wXTJz4a1UDsMaIMLiUuzq85tKnM=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeResources</key>
		<data>
		8rtx8mEXa+s8SVvq31IMg2Dk+UI=
		</data>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeSignature</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/VGuard.framework/Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGuard-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			OYp+k432cGOPdFc7rWW0Gz+maSM=
			</data>
			<key>hash2</key>
			<data>
			0eRr8+r8FX8kkG/w9kvCygy2ikxjXRUSdIA0w1vUhHw=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			H0coipAoUJoXGsPDSioqJIcIQfY=
			</data>
			<key>hash2</key>
			<data>
			8c3JAYdKu6Fx2vz8GiDp/EAAYy9SqXpv7EtYDCHPtBw=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			CDZGRDZuMS78xqyMrkdwitrnm8M=
			</data>
			<key>hash2</key>
			<data>
			9Xsc7OaKNq0VV7ja6Z5xvBfu9zgrrZKTYWiwshl9G3Q=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			51zmKixeJCA2EXyytbh+qBlU+7g=
			</data>
			<key>hash2</key>
			<data>
			MxbxqvzedaXsYvZ58X+98j2eeIsvReGI7lHhhR6IfA8=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			SE8x/OoInHPM0Y2ZPXsNzYiSY84=
			</data>
			<key>hash2</key>
			<data>
			Dz0Y9b2KVwZGv3gSVe3qZnIu7QRAVxHd0S7acl4xOc4=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			WS7+91RG1ccn8iEGH0WyzMkj3Gw=
			</data>
			<key>hash2</key>
			<data>
			CD0bU+Q7t+Q0Yp57p9smWrhbUm7+n8qvRpL5im9YfpU=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
			</data>
			<key>hash2</key>
			<data>
			E/4VLnY+n150X+k8vAMmzjbPqJfXRlVObvaLoERDWnM=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			deygybyRFZyPLessWeVWafBzlRA=
			</data>
			<key>hash2</key>
			<data>
			tvKRGQlwQ+nvbzzUStKu671D4+eYLLQG2yfWZU8fprU=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
			</data>
			<key>hash2</key>
			<data>
			E/4VLnY+n150X+k8vAMmzjbPqJfXRlVObvaLoERDWnM=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			E4/CbwB3LVIhIc0LUz7RYzd8As8=
			</data>
			<key>hash2</key>
			<data>
			0pMFMn2XVP5MKgrbUPaEqJaaPrC63D+XUW4UtHlID6A=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/VGuard</key>
		<dict>
			<key>hash</key>
			<data>
			ZCkjmwR8oQXmZF5kbPQXYWjlHqU=
			</data>
			<key>hash2</key>
			<data>
			cBDVD5wPx1O5tRdC4VjNxvdffYxKSgBzZf4b65+N5ws=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			bPvAvMmqCHi0b8w3G0Qdp3CTpX0=
			</data>
			<key>hash2</key>
			<data>
			vowYaGGyX73txU1vS9Hi6f46ZYlVCyBDzajZEo4y7kY=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			2l9s6dAcnxvuIUkq/UjAr72SwXM=
			</data>
			<key>hash2</key>
			<data>
			PTFv34egTIoeTR8NuM3/it45lLpkvGq0fUhbO0YEPF4=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			SWUMFGibgWcSsLKuf0ecY/l8nNE=
			</data>
			<key>hash2</key>
			<data>
			5mObI9kENadAOF5EqgGcI1ROiVeXTmkom5WoHiLolAo=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			lk/ksZHU9fWtTAvidnJ0XVOuoOc=
			</data>
			<key>hash2</key>
			<data>
			sl2UUSVYKgR/uYKyZvkdRv3/DtsU/PZ0j9EF68Gha1A=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			aI/eMN7VtC/OR2z7SARH6aRtcU8=
			</data>
			<key>hash2</key>
			<data>
			d4A4SopJszW44PhvJdOlpoPnAxBMkQ0TJWp4a6+qU+o=
			</data>
		</dict>
		<key>ios-arm64/VGuard.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			GC4R//86Y61yZ1aC/jBlOREwiYo=
			</data>
			<key>hash2</key>
			<data>
			n/FImrY+r+E+DWU2rV69R1RixFnijJc7LwZbe6EcOxM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuard-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			ospQqtertB8x3u4IoFLO3MibNHE=
			</data>
			<key>hash2</key>
			<data>
			4WkEnJ7ZBXl1M6ybiDRmguIHEPF3QIVSD5kPV+Hi3o0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			H0coipAoUJoXGsPDSioqJIcIQfY=
			</data>
			<key>hash2</key>
			<data>
			8c3JAYdKu6Fx2vz8GiDp/EAAYy9SqXpv7EtYDCHPtBw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			CDZGRDZuMS78xqyMrkdwitrnm8M=
			</data>
			<key>hash2</key>
			<data>
			9Xsc7OaKNq0VV7ja6Z5xvBfu9zgrrZKTYWiwshl9G3Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			51zmKixeJCA2EXyytbh+qBlU+7g=
			</data>
			<key>hash2</key>
			<data>
			MxbxqvzedaXsYvZ58X+98j2eeIsvReGI7lHhhR6IfA8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			SE8x/OoInHPM0Y2ZPXsNzYiSY84=
			</data>
			<key>hash2</key>
			<data>
			Dz0Y9b2KVwZGv3gSVe3qZnIu7QRAVxHd0S7acl4xOc4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			5piOf7bBwpnJVe4Hi9bM7mzA9qE=
			</data>
			<key>hash2</key>
			<data>
			NPRFIQVVhm5cKdKwxa6uYAl+OexNIpvqUZWJLwswSNk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tfN11YPD+r75K/S3FnU6w34JtiM=
			</data>
			<key>hash2</key>
			<data>
			j33VYqUyv38X7j+5SQTfvWXhu/otxMFgzb1noN0wfwg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			zP4/zr3D09PKspKAJdzgdpiIVuk=
			</data>
			<key>hash2</key>
			<data>
			7Gucawi8FpoP0Eu3WiWst8zMAFncCLxrlzvqmQrBHBE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tfN11YPD+r75K/S3FnU6w34JtiM=
			</data>
			<key>hash2</key>
			<data>
			j33VYqUyv38X7j+5SQTfvWXhu/otxMFgzb1noN0wfwg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			pl12kK57xnpbgdM8/Dfygk3fiCk=
			</data>
			<key>hash2</key>
			<data>
			0fm+LEFH5PRI/Kuvtw8gep8qBQlSTAqU8sf/MDQpRbo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			EFmglgeL94Qrj9qEaKHsmqUb1Jw=
			</data>
			<key>hash2</key>
			<data>
			HoN6vX8l+TTmBl6E0pF8stGq6ikNo1jDVnhjQ+NOImE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			pl12kK57xnpbgdM8/Dfygk3fiCk=
			</data>
			<key>hash2</key>
			<data>
			0fm+LEFH5PRI/Kuvtw8gep8qBQlSTAqU8sf/MDQpRbo=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			E4/CbwB3LVIhIc0LUz7RYzd8As8=
			</data>
			<key>hash2</key>
			<data>
			0pMFMn2XVP5MKgrbUPaEqJaaPrC63D+XUW4UtHlID6A=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/VGuard</key>
		<dict>
			<key>hash</key>
			<data>
			Cj99QynB9Y1zlb4WM4XBkQOERY8=
			</data>
			<key>hash2</key>
			<data>
			/GOskbhozoTG08Bhc0b1cXJICzTJzAhbE372kBDDayM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			bPvAvMmqCHi0b8w3G0Qdp3CTpX0=
			</data>
			<key>hash2</key>
			<data>
			vowYaGGyX73txU1vS9Hi6f46ZYlVCyBDzajZEo4y7kY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeDirectory</key>
		<dict>
			<key>hash</key>
			<data>
			A8ml5TEU8ZPzT/lXkm7pPgDh7L0=
			</data>
			<key>hash2</key>
			<data>
			FILzq+TbE95ANtZV0ETOwRDyUuZhJH1cGS9HaCfnM9w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements</key>
		<dict>
			<key>hash</key>
			<data>
			OnX22wWFKRSOFN1+obRynMCeyXM=
			</data>
			<key>hash2</key>
			<data>
			mHkgkE6rZQ51eIwFSqCwUk5qgL/HGqMt+NI3phdD+YY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeRequirements-1</key>
		<dict>
			<key>hash</key>
			<data>
			wXTJz4a1UDsMaIMLiUuzq85tKnM=
			</data>
			<key>hash2</key>
			<data>
			zOG9NcqI7QfPYzaABHj9tHf8Cq6ALs/yBDpxuysrRCA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			8rtx8mEXa+s8SVvq31IMg2Dk+UI=
			</data>
			<key>hash2</key>
			<data>
			J9a5gbpb3OVEqUL+2KOfJrUf80GuWcZMfvd2c6xdZcQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/VGuard.framework/_CodeSignature/CodeSignature</key>
		<dict>
			<key>hash</key>
			<data>
			2jmj7l5rSw0yVb/vlWAYkK/YBwk=
			</data>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
