//
//  VGuardThreats.h
//  VGuard
//
//  Created by <PERSON><PERSON><PERSON> on 12/6/14.
//  Copyright (c) 2014 V-Key Pte Ltd. All rights reserved.
//

#import <Foundation/Foundation.h>

@protocol VGuardThreatsDelegate <NSObject>

@optional

- (void)vGuardScan:(NSArray *)threatsArray;

- (void)vGuardDidDetectThreats:(NSDictionary *)threatsInfo;

- (void)vGuardDidDetectScreenSharing;

@end


@interface VGuardThreats : NSObject

@property (weak, nonatomic) id<VGuardThreatsDelegate> delegate;

+ (VGuardThreats *)sharedModule;

/**
 * To register view instance for Virtual Tap detection
 *
 * @discusstion detection can be added from parent view.
 * E.g ViewController.view, all subviews will have detecion.
 */
- (void)addVirtualTapDetection:(UIView * _Nonnull)view;

/**
 * This method check screen sharing status
 */
- (void)checkScreenSharing;

@end
