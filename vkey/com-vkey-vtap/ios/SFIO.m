//
//  SFIO.h
//  RNVosSmarttokenReactNative
//
//  Created by Tu 04/18/2025.
//  Copyright (c) 2025 V-Key Pte Ltd. All rights reserved.
//
#import "SFIO.h"
#import "VTapPlugin.h"
#import <React/RCTLog.h>
#import "React/RCTBridgeModule.h"

@interface SFIO (){
}
@end


@implementation SFIO

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

RCT_EXPORT_MODULE();

- (NSData *)readableArrayToByteArray:(NSArray<NSNumber *> *)array {
    NSMutableData *data = [NSMutableData dataWithCapacity:array.count];
    for (NSNumber *number in array) {
        uint8_t byte = [number unsignedCharValue];
        [data appendBytes:&byte length:1];
    }
    return [data copy];
}

- (NSArray<NSNumber *> *)mutableDataToArray:(NSMutableData *)data {
    NSMutableArray<NSNumber *> *array = [NSMutableArray arrayWithCapacity:data.length];
    const uint8_t *bytes = data.bytes;
    for (NSUInteger i = 0; i < data.length; i++) {
        [array addObject:@(bytes[i])];
    }
    return [array copy];
}

/*
+ (int) encryptData:(NSData *)data
             output:(NSMutableData *)outputData;
*/
RCT_EXPORT_METHOD(encryptData:(NSArray<NSNumber *> *)array
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    NSData *data = [self readableArrayToByteArray:array];
    NSMutableData *outputData = [NSMutableData data];
    int result = [SecureFileIO encryptData:data output:outputData];
    if (result >= 0) {
        resolve([self mutableDataToArray: outputData]);
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"encryptData_error", errorMessage, nil);
    }
}

/*
+ (int) encryptData:(NSData*)data
              toFile:(NSString *)path
            password:(NSString *)password
          atomically:(BOOL)useAuxiliaryFile;
*/
RCT_EXPORT_METHOD(encryptDataToFile:(NSArray<NSNumber *> *)array
                  toFile:(NSString *)path
                  password:(NSString *)password
                  atomically:(BOOL)useAuxiliaryFile
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    NSData *data = [self readableArrayToByteArray:array];
    NSMutableData *outputData = [NSMutableData data];
    int result = [SecureFileIO encryptData:data toFile:path password:password atomically:useAuxiliaryFile];
    if (data != nil && result > 0) {
        resolve(@(result));
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"encryptDataToFile", @"fail", nil);
    }
}

//+ (int) decryptData:(NSData *)cipher
//             output:(NSMutableData *)normal;


RCT_EXPORT_METHOD(decryptData:(NSArray<NSNumber *> *)array
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    NSMutableData *outputData = [NSMutableData data];
    int result = [SecureFileIO decryptData:[self readableArrayToByteArray:array] output:outputData];
    
    if (result >= 0) {
        if (outputData != nil) {
            resolve([self mutableDataToArray: outputData]);
        } else {
            reject(@"decryptData_encoding_error", @"Decrypted data is not valid UTF-8", nil);
        }
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"decryptData_error", errorMessage, nil);
    }
}

/*
+ (int) encryptString:(NSString*)str
                toFile:(NSString *)path
              password:(NSString *)password
            atomically:(BOOL)useAuxiliaryFile;
*/
RCT_EXPORT_METHOD(encryptString:(NSString *)str
                  toFile:(NSString *)path
                  password:(NSString *)password
                  atomically:(BOOL)useAuxiliaryFile
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    int result = [SecureFileIO encryptString:str toFile:path password:password atomically:useAuxiliaryFile];
    
    if (result >= 0) {
        resolve(@(result));
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"encryptString_error", errorMessage, nil);
    }
}

/*
+ (int) decryptStringFromFile:(NSString *)path
                     password:(NSString *)password
                       output:(NSMutableString *)decryptedString;
*/
RCT_EXPORT_METHOD(decryptStringFromFile:(NSString *)path
                  password:(NSString *)password
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    NSMutableString *outputString = [NSMutableString string];
    int result = [SecureFileIO decryptStringFromFile:path password:password output:outputString];
    
    if (result >= 0) {
        resolve(outputString);
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"decryptString_error", errorMessage, nil);
    }
}

/*
+ (int) decryptFile:(NSString *)path
           password:(NSString *)password
             output:(NSMutableData *)outputData;
*/
RCT_EXPORT_METHOD(decryptFile:(NSString *)path
                  password:(NSString *)password
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    NSMutableData *outputData = [NSMutableData data];
    int result = [SecureFileIO decryptFile:path password:password output:outputData];
    
    if (result >= 0) {
        resolve([self mutableDataToArray: outputData]);
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"decryptFile_error", errorMessage, nil);
    }
}

/*
+ (int) updateFile:(NSString *)path
       newPassword:(NSString *)newPassword
       oldPassword:(NSString *)oldPassword;
*/
RCT_EXPORT_METHOD(updateFile:(NSString *)path
                  newPassword:(NSString *)newPassword
                  oldPassword:(NSString *)oldPassword
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    int result = [SecureFileIO updateFile:path newPassword:newPassword oldPassword:oldPassword];
    
    if (result >= 0) {
        resolve(@(result));
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"updateFile_error", errorMessage, nil);
    }
}

/*
 + (int) encryptFile:(NSString *)path
         newPassword:(NSString *)newPassword;
 */
RCT_EXPORT_METHOD(encryptFile:(NSString *)path
                  newPassword:(NSString *)newPassword
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    int result = [SecureFileIO encryptFile:path newPassword:newPassword];
    
    if (result >= 0) {
        resolve(@(result));
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"encryptFile_error", errorMessage, nil);
    }
}

/*
 + (int) encryptFileFrom:(NSString *)from
                      to:(NSString *)to
             newPassword:(NSString *)newPassword;
 */
RCT_EXPORT_METHOD(encryptFileFrom:(NSString *)from
                  to:(NSString *)to
                  newPassword:(NSString *)newPassword
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject) {
    int execRet = [VosWrapper execute:nil];
    if (execRet < 0) {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", execRet];
        reject(@"VOS_error", errorMessage, nil);
    }
    
    int result = [SecureFileIO encryptFileFrom:from to:to newPassword:newPassword];
    
    if (result >= 0) {
        resolve(@(result));
    } else {
        NSString *errorMessage = [NSString stringWithFormat:@"%d", result];
        reject(@"encryptFileFromTo_error", errorMessage, nil);
    }
}

@end
