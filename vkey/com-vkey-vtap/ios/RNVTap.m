//
//  RNVGuard.m
//  RNVosSmarttokenReactNative
//
//  Created by <PERSON><PERSON><PERSON> (An) on 5/15/19.
//  Copyright © 2019 Facebook. All rights reserved.
//

#import "RNVTap.h"
#import <React/RCTLog.h>

typedef enum {
    MESSAGE_TYPE,
    PUSH_NOTIFICATION_REGISTER,
    MESSAGE_TYPE_SMP_MSG,
    MESSAGE_TYPE_ASP_CERT,
    MESSAGE_TYPE_SMP_CERT,
    MESSAGE_TYPE_ASP_DOC_CERT,
    MESSAGE_TYPE_AUTH,
    MESSAGE_TYPE_DOC_SIGN,
    MESSAGE_TYPE_ASP_CERT_RENEW,
    MESSAGE_TYPE_SMP_CERT_RENEW,
    MESSAGE_TYPE_ASP_DOC_CERT_RENEW
} AdditionDataType;

@interface RNVTap (){
    VTapManager* vtapMgr;
    
    RCTResponseSenderBlock cb1;
    NSString *logString;
    
    NSString *ts;
    NSString *apin;
}
//@property (strong, nonatomic) NSString *logString;

@end

@implementation RNVTap

#define TOKEN_MEANS     0
#define ERROR           -1


- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

RCT_EXPORT_MODULE();

- (NSDictionary *)constantsToExport
{
    return @{
//              @"MESSAGE_TYPE": @(MESSAGE_TYPE),
//              @"MESSAGE_TYPE_AUTH": @(MESSAGE_TYPE_AUTH),
//              @"MESSAGE_TYPE_DOC_SIGN": @(MESSAGE_TYPE_DOC_SIGN),
//              @"MESSAGE_TYPE_SMP_MSG": @(MESSAGE_TYPE_SMP_MSG),
//              @"MESSAGE_TYPE_ASP_CERT": @(MESSAGE_TYPE_ASP_CERT),
//              @"MESSAGE_TYPE_ASP_DOC_CERT":@(MESSAGE_TYPE_ASP_DOC_CERT),
//              @"MESSAGE_TYPE_SMP_CERT": @(MESSAGE_TYPE_SMP_CERT),
//              @"MESSAGE_TYPE_ASP_CERT_RENEW":@(MESSAGE_TYPE_ASP_CERT_RENEW),
//              @"MESSAGE_TYPE_ASP_DOC_CERT_RENEW":@(MESSAGE_TYPE_ASP_DOC_CERT_RENEW),
//              @"MESSAGE_TYPE_SMP_CERT_RENEW":@(MESSAGE_TYPE_SMP_CERT_RENEW),
//              @"PUSH_NOTIFICATION_REGISTER":@(PUSH_NOTIFICATION_REGISTER)
             
//             @"AdditionDataType_MESSAGE_TYPE": @(MESSAGE_TYPE),
//             @"AdditionDataType_MESSAGE_TYPE_AUTH": @(MESSAGE_TYPE_AUTH),
//             @"AdditionDataType_MESSAGE_TYPE_DOC_SIGN": @(MESSAGE_TYPE_DOC_SIGN),
//             @"AdditionDataType_MESSAGE_TYPE_SMP_MSG": @(MESSAGE_TYPE_SMP_MSG),
//             @"AdditionDataType_MESSAGE_TYPE_ASP_CERT": @(MESSAGE_TYPE_ASP_CERT),
//             @"AdditionDataType_MESSAGE_TYPE_ASP_DOC_CERT":@(MESSAGE_TYPE_ASP_DOC_CERT),
//             @"AdditionDataType_MESSAGE_TYPE_SMP_CERT": @(MESSAGE_TYPE_SMP_CERT),
//             @"AdditionDataType_MESSAGE_TYPE_ASP_CERT_RENEW":@(MESSAGE_TYPE_ASP_CERT_RENEW),
//             @"AdditionDataType_MESSAGE_TYPE_ASP_DOC_CERT_RENEW":@(MESSAGE_TYPE_ASP_DOC_CERT_RENEW),
//             @"AdditionDataType_MESSAGE_TYPE_SMP_CERT_RENEW":@(MESSAGE_TYPE_SMP_CERT_RENEW),
//             @"AdditionDataType_PUSH_NOTIFICATION_REGISTER":@(PUSH_NOTIFICATION_REGISTER)
             
             @"AdditionDataType_MESSAGE_TYPE": @"MESSAGE_TYPE",
             @"AdditionDataType_MESSAGE_TYPE_AUTH": @"MESSAGE_TYPE_AUTH",
             @"AdditionDataType_MESSAGE_TYPE_DOC_SIGN": @"MESSAGE_TYPE_DOC_SIGN",
             @"AdditionDataType_MESSAGE_TYPE_SMP_MSG": @"MESSAGE_TYPE_SMP_MSG",
             @"AdditionDataType_MESSAGE_TYPE_ASP_CERT": @"MESSAGE_TYPE_ASP_CERT",
             @"AdditionDataType_MESSAGE_TYPE_ASP_DOC_CERT":@"MESSAGE_TYPE_ASP_DOC_CERT",
             @"AdditionDataType_MESSAGE_TYPE_SMP_CERT": @"MESSAGE_TYPE_SMP_CERT",
             @"AdditionDataType_MESSAGE_TYPE_ASP_CERT_RENEW":@"MESSAGE_TYPE_ASP_CERT_RENEW",
             @"AdditionDataType_MESSAGE_TYPE_ASP_DOC_CERT_RENEW":@"MESSAGE_TYPE_ASP_DOC_CERT_RENEW",
             @"AdditionDataType_MESSAGE_TYPE_SMP_CERT_RENEW":@"MESSAGE_TYPE_SMP_CERT_RENEW",
             @"AdditionDataType_PUSH_NOTIFICATION_REGISTER":@"PUSH_NOTIFICATION_REGISTER"

             };
}

+ (BOOL)requiresMainQueueSetup
{
    return YES;  // only do this if your module initialization relies on calling UIKit!
}

// Generic : Set Up VKey Components API
RCT_EXPORT_METHOD(setupEnvironment:(NSString *)serverUrl) {
    vtapMgr = [VTapManager sharedInstance];
    NSString *provServer = [NSString stringWithFormat: @"%@%@", serverUrl, @"/provision"];
    NSString *vtapServer = [NSString stringWithFormat: @"%@%@", serverUrl, @"/vtap"];
    [vtapMgr setHostName:provServer vtapServer:vtapServer];
    
    NSString *pkiServer = serverUrl;
    [vtapMgr setPKIHostName:pkiServer];
    
    [vtapMgr setDelegate:self];
    
//    [self setupVtapAndSecureKeypad];
    
    logString = @"abc";
}

- (void) setupVtapAndSecureKeypad {
    [vtapMgr setupVTap];
    
    VKeySecureKeypad *kb = [VKeySecureKeypad sharedModule];
    [kb setEnableKeyboard:YES];
    [kb setEnableScrambleKeypad:YES];
    
    [kb setKeypadButtonAlpha:0.5];
    [kb setKeyboardButtonBackgroundColor:[UIColor redColor]];
    [kb setKeypadTextColor:[UIColor redColor]];
}

RCT_EXPORT_METHOD(setupVTap:(RCTResponseSenderBlock)succCB) {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        // Call long-running code on background thread
        [self setupVtapAndSecureKeypad];
        // You can invoke callback from any thread/queue
        
//        NSArray *results =  [NSArray arrayWithObjects:logString, nil];
//        succCB(@[[NSNull null], results]);
    });
}

RCT_EXPORT_METHOD(provisionToken:(RCTResponseSenderBlock)succCB) {
    NSString *logProvision = @"";
    bool provisioningStatus = [vtapMgr isProvisioningDone];
    logProvision = [NSString stringWithFormat:@"%@%@%d", logProvision, @"\n\n isProvisioningDone: ", provisioningStatus];
    
    ts = @"CR9E342DAF";
    apin = @"2nXb7byuGaWPRfNtHVjMn2UTpTmLKRauJWNb2ELCgmcNck";
    
//    VKCR9E342DAF, 2nXb7byuGaWPRfNtHVjMn2UTpTmLKRauJWNb2ELCgmcNck
//    VKJPA4C66EEE, CTPZYexirTxqoh4CrsLNEczwriY6JoLoizyFJ9hJriwsRx
    bool isValidateChecksumSucceded = [vtapMgr validateChecksum:[NSArray arrayWithObjects:ts, apin, nil]];
    logProvision = [NSString stringWithFormat:@"%@%@%d", logProvision, @"\n\n APIN length: ", isValidateChecksumSucceded];
    
//    NSData *decodedData;
//    if (apin.length == 88 || apin.length == 108) {
//        //encrypted APIN need to base64 decode and hex
//        if ([apin respondsToSelector:@selector(initWithBase64EncodedString:options:)]) {
//            decodedData = [[NSData alloc] initWithBase64EncodedString:apin options:0];
//        }
//        else{
//            decodedData = [NSData dataFromBase64String:apin];
//        }
//        apin = [[decodedData hexadecimalString] mutableCopy];
//    }
    
    int getTokenFirmwareStatus;
    if (TOKEN_MEANS == 0) {
        getTokenFirmwareStatus = [vtapMgr getLoadAckTokenFirmware:[NSArray arrayWithObjects:ts, apin, nil]];
    }
    else{
        //Can not test this scenario:tested by dev team-13Oct
        //If want to test DBS customised API directly using firmware.json instead of downloading it from the server
        NSString *filePath = [[NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) objectAtIndex:0] stringByAppendingPathComponent:@"firmware.json"];
        
        NSData *data = [[NSData alloc] initWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"firmware" ofType:@"json"]];
        [data writeToFile:filePath atomically:YES];
        getTokenFirmwareStatus = [vtapMgr loadTokenFirmware:ts APIN:apin downloadFilePath:filePath];
    }
    
    logProvision = [NSString stringWithFormat:@"%@%@%d", logProvision, @"\n\n 9. getLoadAckTokenFirmware: ", getTokenFirmwareStatus];

    if(getTokenFirmwareStatus == VTAP_TOKEN_DOWNLOAD_SUCCESS || getTokenFirmwareStatus == VTAP_LOAD_FIRMWARE_SUCCESS){
        // TokenType tokenType;
//        dispatch_async(dispatch_get_main_queue(), ^{
//            _textView.text = [_textView.text stringByAppendingString:[NSString stringWithFormat:@"\n\n Provisioning for TS:%@ is done with result: %d",ts, getTokenFirmwareStatus]];
//        });
        
        NSString *loadedPKIToken = [vtapMgr getPKITokenSerial];
        NSString *loadedOTPToken = [vtapMgr getTokenSerial];
        
//        dispatch_async(dispatch_get_main_queue(), ^{
//            _textView.text = [_textView.text stringByAppendingString:[NSString stringWithFormat:@"\n\n loaded PKI token: %@ \n loaded OTP Token:%@",loadedPKIToken, loadedOTPToken]];
//        });
        
        // 20. Get Firmware Version
        NSString *firmwareVersion = [vtapMgr getTokenFirmwareVersion:ts];
//        dispatch_async(dispatch_get_main_queue(), ^{
//            _textView.text = [_textView.text stringByAppendingString:[NSString stringWithFormat:@"\n\n getTokenFirmwareVersion: %@",firmwareVersion]];
//        });
//        [self accessTokenPinFunctions];
//
//        [self resetCheckPinOptions];
    }
    
    
    NSArray *results =  [NSArray arrayWithObjects:logProvision, nil];
    succCB(@[[NSNull null], results]);
}

RCT_EXPORT_METHOD(setHostName:(NSString *) provServer vtapServer:(NSString *) vtapServer) {
    [vtapMgr setHostName:provServer vtapServer:vtapServer];
}

RCT_EXPORT_METHOD(setupVtap) {
    if (vtapMgr == nil) {
        vtapMgr = [VTapManager sharedInstance];
    }
    [vtapMgr setupVTap];
}

- (void)didStartVTap:(int)statusCode {
    NSLog(@"finish start v tap");
    if (statusCode == VTAP_SETUP_SUCCESS) {
        logString = @"VTAP_SETUP_SUCCESS";
    }
    else {
        logString = @"VTAP_SETUP_fail";
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString*, getDFPHash) {
//    if (vtapMgr != nil) {
//        return [vtapMgr getDFPHash];
//    }
//    return nil;
//}

RCT_EXPORT_METHOD(getDFPHash:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSString* dfpHash = [vtapMgr getDFPHash];
        resolve(dfpHash);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, isProvisioningDone) {
//    if (vtapMgr != nil) {
//        return [vtapMgr isProvisioningDone];
//    }
//    return false;
//}

RCT_EXPORT_METHOD(isProvisioningDone:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        bool isProvisioningDone = [vtapMgr isProvisioningDone];
        resolve([NSNumber numberWithBool:isProvisioningDone]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray*, getAllProvisionedTokens) {
//    return [vtapMgr getAllProvisionedTokens];
//}

RCT_EXPORT_METHOD(getAllProvisionedTokens:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSArray* provisionedTokens = [vtapMgr getAllProvisionedTokens];
        resolve(provisionedTokens);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, getTokenType:(NSString*)tokenSerial) {
//    if (vtapMgr != nil) {
//        return [vtapMgr getTokenType:tokenSerial];
//    }
//    return UNKNOWN_TOKEN;
//}

RCT_EXPORT_METHOD(getTokenType:(NSString*)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        int tkType = [vtapMgr getTokenType:tokenSerial];
        resolve([NSNumber numberWithInt:tkType]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// Generic : Lifecycle Actions API
RCT_EXPORT_METHOD(onResume) {
    [vtapMgr onResume];
}

RCT_EXPORT_METHOD(onPause) {
    [vtapMgr onPause];
}

RCT_EXPORT_METHOD(onDestroy) {
    [vtapMgr onDestroy];
}

// Generic : Troubleshooting Logs API
//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString*, getTroubleshootingId) {
//    if (vtapMgr != nil) {
//        return [vtapMgr getTroubleshootingId];
//    }
//    return nil;
//}

RCT_EXPORT_METHOD(getTroubleshootingId:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSString* troubleshootingId = [vtapMgr getTroubleshootingId];
        resolve(troubleshootingId);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, sendTroubleshootingLogs) {
//    return [vtapMgr sendTroubleshootingLogs];
//}

RCT_EXPORT_METHOD(sendTroubleshootingLogs:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        int result = [vtapMgr sendTroubleshootingLogs];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//  Generic : Device Check API

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, checkDeviceCompatibility) {
//    return [vtapMgr checkDeviceCompatibility];
//}

RCT_EXPORT_METHOD(checkDeviceCompatibility:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        int result = [vtapMgr checkDeviceCompatibility];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_METHOD(sendDeviceInfo:(NSString*)deviceInfo status:(int)stt) {
//    [vtapMgr sendDeviceInfo:deviceInfo status:stt];
//}

RCT_EXPORT_METHOD(sendDeviceInfo:(NSString*)deviceInfo status:(int)stt resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        int result = [vtapMgr sendDeviceInfo:deviceInfo status:stt];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// Luke Pham added for new rest APIs
RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, setAdditionalData:(NSString*)data additionalDataType:(NSString *)dataType) {
    int result = [vtapMgr setAdditionalData:data additionalDataType:dataType];
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt:result];
    }
    return [NSNumber numberWithInt:-1];
}

-(NSString*)convertToAdditionalData:(int)type {
    switch (type) {
        case MESSAGE_TYPE:
            return @"MESSAGE_TYPE_AUTH";
        case MESSAGE_TYPE_DOC_SIGN:
            return @"MESSAGE_TYPE_DOC_SIGN";
        case MESSAGE_TYPE_SMP_MSG:
            return @"MESSAGE_TYPE_SMP_MSG";
        case MESSAGE_TYPE_ASP_CERT:
            return @"MESSAGE_TYPE_ASP_CERT";
        case MESSAGE_TYPE_ASP_DOC_CERT:
            return @"MESSAGE_TYPE_ASP_DOC_CERT";
        case MESSAGE_TYPE_SMP_CERT:
            return @"MESSAGE_TYPE_SMP_CERT";
        case MESSAGE_TYPE_ASP_CERT_RENEW:
            return @"MESSAGE_TYPE_ASP_CERT_RENEW";
        case MESSAGE_TYPE_ASP_DOC_CERT_RENEW:
            return @"MESSAGE_TYPE_ASP_DOC_CERT_RENEW";
        case MESSAGE_TYPE_SMP_CERT_RENEW:
            return @"MESSAGE_TYPE_SMP_CERT_RENEW";
        case PUSH_NOTIFICATION_REGISTER:
            return @"PUSH_NOTIFICATION_REGISTER";
    }
    return @"";
}

//RCT_EXPORT_METHOD(setAdditionalData:(NSString*)data additionalDataType:(NSString*)dataType resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
//    if (vtapMgr != nil) {
////        NSString* dataToString = [self convertToAdditionalData:data];
//        int result = [vtapMgr setAdditionalData:data additionalDataType:dataType];
//        resolve([NSNumber numberWithInt:result]);
//    } else {
//        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
//        reject(@"no_events", @"There were no events", error);
//    }
//}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, setMessageTypeData:(NSString*)value) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt:[vtapMgr setMessageTypeData:value]];
    }
    return [NSNumber numberWithInt:-1];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString*, getAdditionalData:(int)dataType) {
    if (vtapMgr != nil) {
        NSString *dataTypeStr = [self convertToAdditionalData:dataType];
        return [vtapMgr getAdditionalData:dataTypeStr];
    }
    return nil;
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, disableAdditionalData:(int)dataType) {
    if (vtapMgr != nil) {
        NSString *dataTypeStr = [self convertToAdditionalData:dataType];
        return [NSNumber numberWithInt:[vtapMgr disableAdditionalData:dataTypeStr]];
    }
    return  [NSNumber numberWithInt:-1];;
}

// OTP TA: Transaction API
//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, setOtpLength:(int)length) {
//    return [vtapMgr setOtpLength:length];
//}
RCT_EXPORT_METHOD(setOtpLength:(int)length resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr setOtpLength:length];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray*, generateTOTP:(int)num) {
//    return [vtapMgr generateTOTP:num];
//}
RCT_EXPORT_METHOD(generateTOTP:(int)num resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSArray *arr =  [vtapMgr generateTOTP:num];
        resolve(arr);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray*, generateCR:(NSString*)crMsg) {
//    return [vtapMgr generateCR:crMsg];
//}

RCT_EXPORT_METHOD(generateCR:(NSString*)crMsg resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSArray *arr = [vtapMgr generateCR:crMsg];
        resolve(arr);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray*, generateTxS:(NSString*)account amount: (NSString*)amount) {
//    return [vtapMgr generateTxS:account amount:amount];
//}

RCT_EXPORT_METHOD(generateTxS:(NSString*)account amount: (NSString*)amount resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    if (vtapMgr != nil) {
        NSArray *arr = [vtapMgr generateTxS:account amount:amount];
        resolve(arr);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString*, getTokenSerial) {
//    return [vtapMgr getTokenSerial];
//}

RCT_EXPORT_METHOD(getTokenSerial:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString *ts = nil;
    if (vtapMgr != nil) {
        ts =[vtapMgr getTokenSerial];
        resolve(ts);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//  Multiple(OTP/PKC) TA : Provisioning API
//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, validateChecksum:(NSArray *)provisioningInfo) {
//    return [vtapMgr validateChecksum:provisioningInfo];
//}

RCT_EXPORT_METHOD(validateChecksum:(NSArray *)provisioningInfo resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL result = false;
    if (vtapMgr != nil) {
        result = [vtapMgr validateChecksum:provisioningInfo];
        resolve([NSNumber numberWithBool:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, getLoadAckTokenFirmware:(NSArray*) provisioningInfo) {
//    return [vtapMgr getLoadAckTokenFirmware:provisioningInfo];
//}
RCT_EXPORT_METHOD(getLoadAckTokenFirmware:(NSArray*)provisioningInfo resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr getLoadAckTokenFirmware:provisioningInfo];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, getLoadAckTokenFirmware:(NSArray*)provisioningInfo httpPostMethod:(BOOL) isHttpPost) {
//    return [vtapMgr getLoadAckTokenFirmware: provisioningInfo httpPostMethod: isHttpPost];
//}

RCT_EXPORT_METHOD(getLoadAckTokenFirmware:(NSArray*)provisioningInfo httpPostMethod:(BOOL) isHttpPost resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr getLoadAckTokenFirmware: provisioningInfo httpPostMethod: isHttpPost];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, loadTokenFirmware:(NSString *)tokenSerial APIN:(NSString *) aPIN downloadFilePath:(NSString *) filePath) {
//    return [vtapMgr loadTokenFirmware:tokenSerial APIN:aPIN downloadFilePath:filePath];
//}

RCT_EXPORT_METHOD(loadTokenFirmware:(NSString *)tokenSerial APIN:(NSString *) aPIN downloadFilePath:(NSString *) filePath resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr loadTokenFirmware:tokenSerial APIN:aPIN downloadFilePath:filePath];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, removeTokenFirmware:(NSString *)tokenSerial) {
//    return [vtapMgr removeTokenFirmware:tokenSerial];
//}

RCT_EXPORT_METHOD(removeTokenFirmware:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr removeTokenFirmware:tokenSerial];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, loadToken:(NSString *)tokenSerial) {
//    return [vtapMgr loadToken:tokenSerial];
//}

RCT_EXPORT_METHOD(loadToken:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr loadToken:tokenSerial];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, unloadToken:(NSString *)tokenSerial) {
//    return [vtapMgr unloadToken: tokenSerial];
//}
RCT_EXPORT_METHOD(unloadToken:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr unloadToken: tokenSerial];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString*, getTokenFirmwareVersion:(NSString *)tokenSerial) {
//    return [vtapMgr getTokenFirmwareVersion: tokenSerial];
//}

RCT_EXPORT_METHOD(getTokenFirmwareVersion:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString* version = nil;
    if (vtapMgr != nil) {
        version = [vtapMgr getTokenFirmwareVersion: tokenSerial];
        resolve(version);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//  Multiple TA: Token PIN Management API

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, isTokenRegistered:(NSString *)tokenSerial) {
//    return [vtapMgr isTokenRegistered: tokenSerial];
//}

RCT_EXPORT_METHOD(isTokenRegistered:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL isRegistered = false;
    if (vtapMgr != nil) {
        isRegistered = [vtapMgr isTokenRegistered: tokenSerial];
        resolve([NSNumber numberWithBool:isRegistered]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, createTokenPIN: (NSString *)pin withTokenSerial:(NSString *)tokenSerial) {
//    return [vtapMgr createTokenPIN:pin withTokenSerial:tokenSerial];
//}
RCT_EXPORT_METHOD(createTokenPIN: (NSString *)pin withTokenSerial:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr createTokenPIN:pin withTokenSerial:tokenSerial];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, checkTokenPIN: (NSString *)pin remember:(BOOL)rememberPin withTokenSerial:(NSString *)tokenSerial) {
//    return [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial];
//}

RCT_EXPORT_METHOD(checkTokenPIN: (NSString *)pin remember:(BOOL)rememberPin withTokenSerial:(NSString *)tokenSerial andSeed:(int)seed resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        if (seed == 0) {
            result = [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial];
        } else {
            result = [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial andSeed:seed];
        }
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, checkTokenPIN: (NSString *)pin remember:(BOOL)rememberPin withTokenSerial:(NSString *)tokenSerial andSeed:(int)seed) {
//    return [vtapMgr checkTokenPIN:pin remember:rememberPin withTokenSerial:tokenSerial andSeed:seed];
//}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, isTokenPINRemembered:(NSString *)tokenSerial) {
//    return [vtapMgr isTokenPINRemembered: tokenSerial];
//}
RCT_EXPORT_METHOD(isTokenPINRemembered:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL result = false;
    if (vtapMgr != nil) {
        result = [vtapMgr isTokenPINRemembered: tokenSerial];
        resolve([NSNumber numberWithBool:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, changeTokenPIN: (NSString *)oldPin withNewPIN:(NSString *)newPin withTokenSerial:(NSString *)tokenSerial) {
//    return [vtapMgr changeTokenPIN:oldPin withNewPIN:newPin withTokenSerial:tokenSerial];
//}

RCT_EXPORT_METHOD(changeTokenPIN:(NSString *)oldPin withNewPIN:(NSString *)newPin withTokenSerial:(NSString *)tokenSerial resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr changeTokenPIN:oldPin withNewPIN:newPin withTokenSerial:tokenSerial];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//PKI TA -  Registration

RCT_EXPORT_METHOD(setPKIHostName:(NSString *)pkiServer) {
    [vtapMgr setPKIHostName: pkiServer];
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString *, getPKITokenSerial) {
//    return [vtapMgr getPKITokenSerial];
//}
RCT_EXPORT_METHOD(getPKITokenSerial:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString* ts = nil;
    if (vtapMgr != nil) {
        ts = [vtapMgr getPKITokenSerial];
        resolve(ts);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// Register APNs Token for Push Notification

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pushNotificationRegister:(NSString *)userId deviceId:(NSString *)deviceId andToken:(NSString *)pushToken) {
//    return [vtapMgr pushNotificationRegister: userId deviceId: deviceId andToken: pushToken];
//}

RCT_EXPORT_METHOD(pushNotificationRegister:(NSString *)userId deviceId:(NSString *)deviceId andToken:(NSString *)pushToken resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pushNotificationRegister: userId deviceId: deviceId andToken: pushToken];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, triggerCertPushNotification:(int)functionID) {
//    return [vtapMgr triggerCertPushNotification: functionID];
//}
RCT_EXPORT_METHOD(triggerCertPushNotification:(int)functionID resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr triggerCertPushNotification: functionID];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString *, getRegisteredUserId) {
//    return [vtapMgr getRegisteredUserId];
//}

RCT_EXPORT_METHOD(getRegisteredUserId:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString* uerId = nil;
    if (vtapMgr != nil) {
        uerId = [vtapMgr getRegisteredUserId];
        resolve(uerId);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString *, getRegisteredDeviceId) {
//    return [vtapMgr getRegisteredDeviceId];
//}

RCT_EXPORT_METHOD(getRegisteredDeviceId:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    NSString* deviceId = nil;
    if (vtapMgr != nil) {
        deviceId = [vtapMgr getRegisteredDeviceId];
        resolve(deviceId);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// PKI Generate ECC Key Pair and Send to server

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, generateCsrAndSend:(int)functionId distinguishedName:(DistinguishedName *) distinguishedName pin:(NSString *)pin andRememberPin:(BOOL)rememberPin) {
//    return [vtapMgr generateCsrAndSend:functionId distinguishedName:distinguishedName pin:pin andRememberPin:rememberPin];
//}
RCT_EXPORT_METHOD(generateCsrAndSend:(int)functionId distinguishedName:(NSDictionary*) distinguishedName pin:(NSString *)pin andRememberPin:(BOOL)rememberPin resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    DistinguishedName* dn = [[DistinguishedName alloc] init] ;
    dn.country = distinguishedName[@"Country"];
    dn.stateName = distinguishedName[@"StateName"];
    dn.localityName = distinguishedName[@"LocalityName"];
    dn.organizationName = distinguishedName[@"OrganizationName"];
    dn.organizationUnit = distinguishedName[@"OrganizationUnit"];
    dn.emailAddress = distinguishedName[@"EmailAddress"];
    if (vtapMgr != nil) {
        result = [vtapMgr generateCsrAndSend:functionId distinguishedName:dn pin:pin andRememberPin:rememberPin];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, generateCsrAndSendSync:(int)functionId distinguishedName:(DistinguishedName *) distinguishedName pin:(NSString *)pin andRememberPin:(BOOL)rememberPin) {
//    return [vtapMgr generateCsrAndSendSync:functionId distinguishedName:distinguishedName pin:pin andRememberPin:rememberPin];
//}
RCT_EXPORT_METHOD(generateCsrAndSendSync:(int)functionId distinguishedName:(NSDictionary *) distinguishedName pin:(NSString *)pin andRememberPin:(BOOL)rememberPin resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    DistinguishedName* dn = [[DistinguishedName alloc] init] ;
    dn.country = distinguishedName[@"Country"];
    dn.stateName = distinguishedName[@"StateName"];
    dn.localityName = distinguishedName[@"LocalityName"];
    dn.organizationName = distinguishedName[@"OrganizationName"];
    dn.organizationUnit = distinguishedName[@"OrganizationUnit"];
    dn.emailAddress = distinguishedName[@"EmailAddress"];
    if (vtapMgr != nil) {
        result = [vtapMgr generateCsrAndSendSync:functionId distinguishedName:distinguishedName pin:pin andRememberPin:rememberPin];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pkiCertDownload:(int)functionId  messageId:(NSString *)messageId andMessageType:(NSString *)messageType) {
//    return [vtapMgr pkiCertDownload:functionId messageId:messageId andMessageType:messageType];
//}
RCT_EXPORT_METHOD(pkiCertDownload:(int)functionId messageId:(NSString *)messageId andMessageType:(NSString *)messageType resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pkiCertDownload:functionId messageId:messageId andMessageType:messageType];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// PKI PIN Management

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, isPKIFunctionRegistered:(int)functionId) {
//    return [vtapMgr isPKIFunctionRegistered:functionId];
//}
RCT_EXPORT_METHOD(isPKIFunctionRegistered:(int)functionId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL result = false;
    if (vtapMgr != nil) {
        result = [vtapMgr isPKIFunctionRegistered:functionId];
        resolve([NSNumber numberWithBool:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(BOOL, isPKIFunctionPinRemembered:(int)functionId) {
//    return [vtapMgr isPKIFunctionPinRemembered: functionId];
//}
RCT_EXPORT_METHOD(isPKIFunctionPinRemembered:(int)functionId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    BOOL result = false;
    if (vtapMgr != nil) {
        result = [vtapMgr isPKIFunctionPinRemembered: functionId];
        resolve([NSNumber numberWithBool:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pkiFunctionCheckPin:(int)functionId withPin:(NSString *)pin andRememberPin:(BOOL) rememberPin) {
//    return [vtapMgr pkiFunctionCheckPin:functionId withPin:pin andRememberPin:rememberPin];
//}
RCT_EXPORT_METHOD(pkiFunctionCheckPin:(int)functionId withPin:(NSString *)pin andRememberPin:(BOOL) rememberPin resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pkiFunctionCheckPin:functionId withPin:pin andRememberPin:rememberPin];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pkiFunctionChangePIN:(int)functionId oldPin:(NSString *)oldPin withNewPIN:(NSString *)newPin) {
//    return [vtapMgr pkiFunctionChangePIN:functionId oldPin:oldPin withNewPIN:newPin];
//}

RCT_EXPORT_METHOD(pkiFunctionChangePIN:(int)functionId oldPin:(NSString *)oldPin withNewPIN:(NSString *)newPin resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)reject) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pkiFunctionChangePIN:functionId oldPin:oldPin withNewPIN:newPin];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        reject(@"no_events", @"There were no events", error);
    }
}

// PKI Signing

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pkiFunctionAuthenticate:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject) {
//    return [vtapMgr pkiFunctionAuthenticate:messageId dataToBeSigned:dataToBeSigned andReject:reject];
//}
RCT_EXPORT_METHOD(pkiFunctionAuthenticate:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pkiFunctionAuthenticate:messageId dataToBeSigned:dataToBeSigned andReject:reject];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, pkiFunctionDocSign:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject) {
//    return [vtapMgr pkiFunctionDocSign:messageId dataToBeSigned:dataToBeSigned andReject:reject];
//}
RCT_EXPORT_METHOD(pkiFunctionDocSign:(NSString *)messageId dataToBeSigned:(NSString *)dataToBeSigned andReject:(BOOL)reject resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr pkiFunctionDocSign:messageId dataToBeSigned:dataToBeSigned andReject:reject];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}

//V-Message

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSString *, vMessageDecrypt:(NSData *)encryptedMsg) {
//    return [vtapMgr vMessageDecrypt:encryptedMsg];
//}
RCT_EXPORT_METHOD(vMessageDecrypt:(NSString *)encryptedMsg resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    NSString* result = nil;
    if (vtapMgr != nil) {
        NSData* encryptedMsgData = nil; //[NSData dataFromBase64String: encryptedMsg];
        result = [vtapMgr vMessageDecrypt:encryptedMsgData];
        resolve(result);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, vMessageDownload:(NSString *)messageId messageType:(NSString *)messageType andMessageFlag:(NSString *)messageFlag) {
//    return [vtapMgr vMessageDownload:messageId messageType:messageType andMessageFlag:messageFlag];
//}
RCT_EXPORT_METHOD(vMessageDownload:(NSString *)messageId messageType:(NSString *)messageType andMessageFlag:(NSString *)messageFlag resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    NSArray* result = nil;
    if (vtapMgr != nil) {
        result = [vtapMgr vMessageDownload:messageId messageType:messageType andMessageFlag:messageFlag];
        resolve(result);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, vMessageAck:(NSString *)messageId) {
//    return [vtapMgr vMessageAck:messageId];
//}
RCT_EXPORT_METHOD(vMessageAck:(NSString *)messageId resolver:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr vMessageAck:messageId];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}

//Remove PKI Function KeyPair

RCT_EXPORT_METHOD(removePKIFunction:(int)functionId) {
    if (vtapMgr != nil) {
        [vtapMgr removePKIFunction:functionId];
    }
}

/*========================= Netrust Customization API Starts =========================*/

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, generateEccCsrWithAlias:(NSString *)inputString andKeyAlias:(int)keyAlias) {
    return [vtapMgr generateEccCsrWithAlias:inputString andKeyAlias:keyAlias];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, deleteEccKeyWithAlias:(int)keyAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt:[vtapMgr deleteEccKeyWithAlias: keyAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, eccSignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias) {
    return [vtapMgr eccSignWithAlias:inputData andKeyAlias:keyAlias];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, eccSha1SignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias) {
    return [vtapMgr eccSha1SignWithAlias:inputData andKeyAlias:keyAlias];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, eccSha256SignWithAlias:(NSData *)inputData andKeyAlias:(int)keyAlias) {
    return [vtapMgr eccSha256SignWithAlias:inputData andKeyAlias:keyAlias];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, eccVerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr eccVerifyWithAlias:inputData andSignature:signature andKeyAlias:keyAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, eccSha1VerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr eccSha1VerifyWithAlias:inputData andSignature:signature andKeyAlias:keyAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, eccSha256VerifyWithAlias:(NSData *)inputData andSignature:(NSData *)signature andKeyAlias:(int)keyAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr eccSha256VerifyWithAlias:inputData andSignature:signature andKeyAlias:keyAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, addCertWithAlias:(NSArray *)certArray andCertAlias:(int)certAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr addCertWithAlias:certArray andCertAlias:certAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, getCertWithAlias:(int)certAlias) {
    return [vtapMgr getCertWithAlias:certAlias];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, deleteCertWithAlias:(int)certAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr deleteEccKeyWithAlias:certAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, isKeyAlias:(int)keyAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr isKeyAlias:keyAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(id, isCertAlias:(int)certAlias) {
    if (vtapMgr != nil) {
        return [NSNumber numberWithInt: [vtapMgr isCertAlias:certAlias]];
    }
    return  [NSNumber numberWithInt: ERROR];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, getKeyAliases) {
    return [vtapMgr getKeyAliases];
}

RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(NSArray *, getCertAliases) {
    return [vtapMgr getCertAliases];
}

//RCT_EXPORT_SYNCHRONOUS_TYPED_METHOD(int, resetTokenFirmware) {
//    return [vtapMgr resetTokenFirmware];
//}
RCT_EXPORT_METHOD(resetTokenFirmware:(RCTPromiseResolveBlock)resolve rejecter:(RCTPromiseRejectBlock)rejectPromise) {
    int result = ERROR;
    if (vtapMgr != nil) {
        result = [vtapMgr resetTokenFirmware];
        resolve([NSNumber numberWithInt:result]);
    } else {
        NSError *error = [NSError errorWithDomain:@"world" code:200 userInfo:nil];
        rejectPromise(@"no_events", @"There were no events", error);
    }
}



@end

