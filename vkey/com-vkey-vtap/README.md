REACT-NATIVE VTap Plugin
==============================

Introduction
------------
V-OS Smart Token SDK (software development kit) provides a complete solution for advanced authentication and authorization on mobile phones. With the V-OS and V-OS App Protection as a security foundation, V-OS Smart Token SDK provides a secure second-factor authentication solution on mobile in a convenient and user-friendly package. The SDK is available as a library for enterprises to integrate into their mobile apps to support user authentication and transaction authorization. V-OS Smart Token solution architecture is premised on the need for the mobile token to remain secure even if the underlying mobile operating system has been compromised.

Integration
------------
1. Get the assets which contains `signature, firmware, vkeylicensepack, and voscodedesign.vky`.
2. Get the latest version of SDKs `(vos-app-protection-android-XXXX.aar , vos-processor-android-XXXX.aar, vos-smarttoken-android-XXXX.aar, otp-android-XXXX.aar, securefileio-android-XXXX.aar)`.
3. Download the profile from `V-OS App Protection Server` dashboard.
4. Get the React Native Smart Token plugin ( com-vkey-vtap ).
```
Note: If you are using Threat Intelligence, make sure you have these assets below:
`manifest.json`
`sig_cfs`
```
5. Get the latest version of cuckoo filter jar file `( cuckoofilter4j-XXX.jar )`.
```
Note: If you are using TLA, make sure you have the asset below: `tla_enc.cer`
```
6. Make sure that you have the java keystore for development/debugging.
7. Add the following lines to build.gradle file in the app folder:
```javascript
buildTypes {
  release {
    minifyEnabled true
    proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    signingConfig signingConfigs.release
    crunchPngs false
  }
  debug {
    signingConfig signingConfigs.release
  }
}
```

Installation
------------

## Getting started
### Android
Open up package.json. Add line:
```javascript
"dependencies": { 
    "com-vkey-vtap": "PATH/com-vkey-vtap",
    ...
}
```

`$ npm install PATH/com-vkey-vtap --save`

### iOS
1. Copy V-Key frameworks to <path to com-vkey-vtap>/ios

2. Go to React Native App Project root folder
yarn add <relative path to com-vkey-vtap>
cd ios
pod install

## Manual installation

### Importing assets
Copy the `firmware, signature, vkeylicensepack, voscodesign.vky, and profile` asset files, provided in the package, into the `android/app/src/main/assets/` folder of your app project.
```
Note: Please copy TLA ( tla_enc.cer ) and TI ( manifest.json , sig_cfs ) related
assets if you are using those features.
```

### Android

1. Open project by Android Studio
- Copy `vos-app-protection-android-xxxx.aar`, `vos-processor-android-xxxx.aar`, `vos-smarttoken-android-XXXX.aar`, `otp-android-XXXX.aar`, `securefileio-android-XXXX.aar` to libs folder inside the android folder of project `:com-vkey-vtap`

2. Open `build.gradle` file (project level) from your application.
- Add this line to the file.
```javascript
allprojects {
    repositories {
        flatDir { dirs "$rootDir/../node_modules/com-vkey-vtap/android/libs" }
    }
}
```

3. Insert the following lines inside the dependencies block in `android/app/build.gradle`:
- in dependencies {...} 
```
implementation project(':com-vkey-vtap')
```

4. Open up `android/app/src/main/java/[...]/MainApplication.java`
- Add `import com.vkey.vtap.VTapPackage;` to the imports at the top of the file.
- Add `packages.add(new VTapPackage());` into `getPackages()` method if this package is not linked automatically.

5. Configure permissions and vkey configs, open `android\src\app\AndroidManifest.xml` file
- Add permissions:
```javascript
<uses-permission android:name="android.permission.READ_PHONE_STATE"/>
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.GET_TASKS" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.WAKE_LOCK" />

<queries>
	<intent>
		<action android:name="android.intent.action.MAIN" />
	</intent>
</queries>
```

- Add this configurations to the `<application>` tag:
```javascript
<application
  android:name=".MainApplication"
  android:label="@string/app_name"
  android:icon="@mipmap/ic_launcher"
  android:roundIcon="@mipmap/ic_launcher_round"
  android:allowBackup="false"
  android:supportsRtl="true"
  android:requestLegacyExternalStorage="true"
  android:theme="@style/AppTheme">
```

- Add these line to `<application>` tag:
```javascript

<service android:name="com.vkey.android.secure.overlay.OverlayService" />


<service
	android:name="com.vkey.android.internal.vguard.cache.ProcessHttpRequestIntentService"
	android:permission="android.permission.BIND_JOB_SERVICE" />

<meta-data
  android:name="android.max_aspect"
  android:value="2.1" />
```


## Supported platforms
-------------------

-	Android
-	iOS

### VTap Integration

```javascript
import { VTapPlugin } from 'com-vkey-vtap';
import { SFIO } from 'com-vkey-vtap';

useEffect(() => {
    DeviceEventEmitter.addListener(VTapPlugin.VGUARD_EVENTS, onVGuardEvents);
    DeviceEventEmitter.addListener(VTapPlugin.VTAP_EVENTS, onVTapEvents);
    setupVTap();
    return () => {
      // Remove the event listener before the component is destroyed.
      DeviceEventEmitter.removeAllListeners(VTapPlugin.VGUARD_EVENTS);
      DeviceEventEmitter.removeAllListeners(VTapPlugin.VTAP_EVENTS);
    };
  }, []);

const setupVTap = () => {
    printLogs('START VTAP');
    // Set url of synchronizing the vos logs if enabled
    VTapPlugin.setHostName('https://intranet.v-key.com:4443/provision', 'https://intranet.v-key.com:4443/vtap');
    VTapPlugin.setupVtap();
  };

const onVTapEvents = async (event: any) => {
    const action = event.action;
    printLogs('. vtap.event.action: ' + event.action);
    if(action == VTapPlugin.VTAP_SETUP_ACTION) {
        const vtapSetupStatus = event.data;
        printLogs('Vtap setup status: '+ vtapSetupStatus);
        if(vtapSetupStatus == 40208) {
          printLogs('Emulator detected!');
        }
        if(vtapSetupStatus == 40204) {
          // start new provisioning token
        }
    }
  };

const onVGuardEvents = async (event: any) => {
    const action = event.action;
    printLogs('. event.action: ' + event.action);

    if (action == VTapPlugin.VOS_READY) {
      printLogs('v-os return code: ' + event.data);

      const errorCode = event.data;

      const tid = await VTapPlugin.getTroubleshootingId();
      printLogs('^TID: ' + tid);

    } else if (action == VTapPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) { // This event is for Android only
      showWarningAlert('app is running on Virtual Space')
    } else if (action == VTapPlugin.VGUARD_OVERLAY_DETECTED) { // This event is for Android only
      showWarningAlert('OVERLAY DETECTED!')
    } else if (action == VTapPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
      if (Platform.OS === 'android') {
        var _data = 'VGUARD_SCREEN_SHARING_DETECTED'
        const data = event.data;
        if (data != null && data.length > 0) {
          for (let i = 0; i < data.length; i++) {
            _data += '\nScreen Name: ' + data[i];
          }
        }
        showWarningAlert('OVERLAY DETECTED!')
      } else if (Platform.OS === 'ios') {
        showWarningAlert('SCREEN_SHARING DETECTED!')
      }

    } else if (action == VTapPlugin.VGUARD_HANDLE_THREAT_POLICY) {
      const data = event.data;
      // printLogs("data: " + data);
      // printLogs('highest_policy: ' + data.highest_policy);
      // printLogs('alertTitle: ' + data.alertTitle);
      // printLogs('alertMessage: ' + data.alertMessage);
      // printLogs('disabledAppExpired: ' + data.disabledAppExpired);
      
      const threats = data.threats;
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          // printLogs(
          //   'Threat Info: ' +
          //   threatInfo.ThreatClass +
          //   ' - ' +
          //   threatInfo.ThreatName +
          //   ' - ' +
          //   threatInfo.ThreatInfo +
          //   ' - ' +
          //   threatInfo.ThreatPackageID,
          // );
        }
      } else {
        printLogs('VGUARD_HANDLE_THREAT_POLICY, no threats found!');
      }
    } else  {
      printLogs('. event.data: ' + event.data);
    }
  };


  sample iOS provision OTP token:
  const provisionOTP = async () => {
    console.log('Trigger VTapPlugin.provisionOTP');
  
    try {
      const ts = 'LT3CC6F62D';  // Replace this with your token serial
      const apin = 'P8UXYP37fes6XJXsWbBGdYxwcMTb5+84zS1tWxKdOMJegTfGJDE6EMwlepJAm8tQRMWQJBjFOoxgczVd1ouHKDr/1KMplsfEkGFrEFF1BqM=';  // Replace this with your APIN value (base64 string)
  
      const resultCode = await VTapPlugin.getLoadAckTokenFirmware([ts, apin]);
    
      if (resultCode === 40600 /* VTAP_TOKEN_DOWNLOAD_SUCCESS */ || resultCode === 40608 /* VTAP_LOAD_FIRMWARE_SUCCESS */) {
        console.log('Provision success 🎉');
        printLogs('Provision success 🎉');
      } else {
        console.log('Provision failed ❌, code:', resultCode);
        printLogs('Provision failed ❌, code: ' + resultCode);
      }
    } catch (error) {
      console.error('Provision OTP failed:', error);
    }
  };

  const generateOTP = async () => {
    const ts = 'LT3CC6F62D';  // Replace this with your token serial
    const defaultTokenPIN = "123456";    
    try {
      const createPin = await VTapPlugin.createTokenPIN(defaultTokenPIN, ts);
      printLogs('createTokenPIN status: ' + createPin);
      const checkpin = await VTapPlugin.checkTokenPIN(defaultTokenPIN, true, ts, 0);
      printLogs('checkTokenPIN status: ' + checkpin);
      const resultCode = await VTapPlugin.setOtpLength(8);
      if (resultCode === 41000 /* VTAP_SET_OTP_LENGTH_SUCCESS */) {
        printLogs('setOtpLength success 🎉');
        const otp = await VTapPlugin.generateTOTP(1);
        printLogs('otp: ' + otp);
      } else {
        printLogs('setOtpLength failed ❌, code: ' + resultCode);
      }
    } catch (error) {
      console.error('Provision OTP failed:', error);
    }
  };
```